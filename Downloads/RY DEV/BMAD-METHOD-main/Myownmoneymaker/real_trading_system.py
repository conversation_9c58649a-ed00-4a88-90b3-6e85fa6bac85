"""
Real Trading System for Live Trading with Real Money
ONLY for authorized admin accounts for testing purposes
"""

import sqlite3
import json
import ccxt
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import os
from decimal import Decimal, ROUND_DOWN, getcontext
import logging
from Myownmoneymaker.analysis.premium_trading_analysis import PremiumTradingAnalysis
from Myownmoneymaker.analysis.auto_trading_analysis import AutoTradingAnalysis

getcontext().prec = 8  # Set decimal precision for financial calculations

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

class RealTradingManager:
    def __init__(self, db_path: str = "trading_bot.db"):
        self.db_path = db_path
        self.init_database()
        
        # Authorized admin IDs for real trading (ONLY YOUR IDs)
        self.authorized_real_traders = [**********, **********]  # Your admin IDs
        
        # Initialize real exchanges (will be configured later)
        self.real_exchanges: Dict[int, ccxt.Exchange] = {}
        
        # Trading limits for safety
        self.max_trade_amount_eur = Decimal('100')  # Maximum €100 per trade for testing
        self.daily_trade_limit = Decimal('500')    # Maximum €500 per day

        # Initialize analysis modules
        self.basic_analysis = AutoTradingAnalysis(db_path)
        self.premium_analysis = PremiumTradingAnalysis(db_path)
        
    def init_database(self) -> None:
        """Initialize real trading tables"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Real trading accounts table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS real_trading_accounts (
                    user_id INTEGER PRIMARY KEY,
                    exchange_name TEXT,
                    api_key_encrypted TEXT,
                    api_secret_encrypted TEXT,
                    is_active BOOLEAN DEFAULT 0,
                    is_testnet BOOLEAN DEFAULT 1,
                    balance_eur REAL DEFAULT 0.0,
                    total_trades INTEGER DEFAULT 0,
                    total_profit REAL DEFAULT 0.0,
                    daily_volume REAL DEFAULT 0.0,
                    last_trade_date DATE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (telegram_id)
                )
            ''')
            
            # Real trade history table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS real_trades (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    exchange TEXT,
                    symbol TEXT,
                    side TEXT, -- 'buy' or 'sell'
                    amount REAL,
                    price REAL,
                    cost REAL,
                    fee REAL,
                    order_id TEXT,
                    status TEXT, -- 'pending', 'filled', 'cancelled', 'failed'
                    profit_loss REAL DEFAULT 0.0,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    order_type TEXT DEFAULT 'market',
                    is_real_money BOOLEAN DEFAULT 1,
                    FOREIGN KEY (user_id) REFERENCES users (telegram_id)
                )
            ''')
            
            # Trading performance tracking
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS trading_performance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    date DATE,
                    total_trades INTEGER DEFAULT 0,
                    successful_trades INTEGER DEFAULT 0,
                    total_volume REAL DEFAULT 0.0,
                    total_profit REAL DEFAULT 0.0,
                    best_trade REAL DEFAULT 0.0,
                    worst_trade REAL DEFAULT 0.0,
                    win_rate REAL DEFAULT 0.0,
                    FOREIGN KEY (user_id) REFERENCES users (telegram_id)
                )
            ''')
            
            # Risk management settings
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS risk_settings (
                    user_id INTEGER PRIMARY KEY,
                    max_trade_amount REAL DEFAULT 50.0,
                    max_daily_volume REAL DEFAULT 200.0,
                    stop_loss_percentage REAL DEFAULT 5.0,
                    take_profit_percentage REAL DEFAULT 10.0,
                    max_open_positions INTEGER DEFAULT 3,
                    risk_level TEXT DEFAULT 'conservative',
                    auto_stop_loss BOOLEAN DEFAULT 1,
                    FOREIGN KEY (user_id) REFERENCES users (telegram_id)
                )
            ''')
            
            conn.commit()
    
    def is_authorized_for_real_trading(self, user_id: int) -> bool:
        """Check if user is authorized for real money trading"""
        return user_id in self.authorized_real_traders
    
    def setup_real_exchange(self, user_id: int, exchange_name: str, api_key: str, api_secret: str, testnet: bool = True) -> Dict:
        """Setup real exchange connection for authorized user"""
        if not self.is_authorized_for_real_trading(user_id):
            return {"success": False, "error": "Not authorized for real trading"}
        
        try:
            # Initialize exchange
            exchange_name_lower = exchange_name.lower()
            if exchange_name_lower == 'kucoin':
                exchange = ccxt.kucoin({
                    'apiKey': api_key,
                    'secret': api_secret,
                    'sandbox': testnet,  # Use testnet for safety
                    'enableRateLimit': True,
                })
            elif exchange_name_lower == 'mexc':
                exchange = ccxt.mexc({
                    'apiKey': api_key,
                    'secret': api_secret,
                    'sandbox': testnet,
                    'enableRateLimit': True,
                })
            else:
                return {"success": False, "error": f"Exchange {exchange_name} not supported"}
            
            # Test connection
            balance = exchange.fetch_balance()
            
            # Store encrypted credentials (simplified - in production use proper encryption)
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO real_trading_accounts 
                    (user_id, exchange_name, api_key_encrypted, api_secret_encrypted, is_active, is_testnet)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (user_id, exchange_name, api_key[:10] + "...", "***", True, testnet))
                conn.commit()
            
            # Store exchange instance
            self.real_exchanges[user_id] = exchange
            
            return {
                "success": True,
                "exchange": exchange_name,
                "testnet": testnet,
                "balance": balance.get('USDT', {}).get('free', 0)
            }
            
        except Exception as e:
            logger.error(f"Failed to setup exchange for user {user_id}: {e}")
            return {"success": False, "error": f"Failed to setup exchange: {str(e)}"}
    
    def get_real_balance(self, user_id: int) -> Dict:
        """Get real account balance"""
        if not self.is_authorized_for_real_trading(user_id):
            return {"success": False, "error": "Not authorized"}
        
        if user_id not in self.real_exchanges:
            return {"success": False, "error": "Exchange not configured"}
        
        try:
            exchange = self.real_exchanges[user_id]
            balance = exchange.fetch_balance()
            
            total_value = Decimal('0')
            for symbol, coin in balance.items():
                free_amount = Decimal(str(coin.get('free', 0)))
                if free_amount > 0:
                    price = Decimal(str(self.get_coin_price_in_usdt(exchange, symbol)))
                    total_value += free_amount * price
            
            return {
                "success": True,
                "balance": balance,
                "usdt_balance": balance.get('USDT', {}).get('free', 0),
                "total_value": float(total_value.quantize(Decimal('0.01'), rounding=ROUND_DOWN))
            }
            
        except Exception as e:
            logger.error(f"Failed to get balance for user {user_id}: {e}")
            return {"success": False, "error": f"Failed to get balance: {str(e)}"}
    
    def get_coin_price_in_usdt(self, exchange, symbol: str) -> float:
        """Get coin price in USDT"""
        try:
            if symbol == 'USDT':
                return 1.0
            
            ticker = exchange.fetch_ticker(f"{symbol}/USDT")
            return ticker['last']
        except Exception as e:
            logger.warning(f"Failed to fetch price for {symbol}: {e}")
            return 0.0
    
    def place_real_order(self, user_id: int, symbol: str, side: str, amount: float, order_type: str = 'market') -> Dict:
        """Place real money order (ONLY for authorized users)"""
        if not self.is_authorized_for_real_trading(user_id):
            return {"success": False, "error": "Not authorized for real trading"}
        
        if user_id not in self.real_exchanges:
            return {"success": False, "error": "Exchange not configured"}
        
        # Safety checks
        current_price = Decimal(str(self.get_current_price(symbol)))
        trade_value = Decimal(str(amount)) * current_price
        if trade_value > self.max_trade_amount_eur:
            return {"success": False, "error": f"Trade amount exceeds maximum (€{self.max_trade_amount_eur})"}
        
        if not self.check_daily_limit(user_id, float(trade_value)):
            return {"success": False, "error": f"Daily trading limit exceeded (€{self.daily_trade_limit})"}
        
        try:
            exchange = self.real_exchanges[user_id]
            
            # Place order
            if order_type == 'market':
                if side == 'buy':
                    order = exchange.create_market_buy_order(symbol, amount)
                else:
                    order = exchange.create_market_sell_order(symbol, amount)
            else:
                return {"success": False, "error": "Only market orders supported for now"}
            
            # Record trade
            self.record_real_trade(user_id, order, symbol, side, amount)
            
            return {
                "success": True,
                "order": order,
                "order_id": order['id'],
                "status": order['status'],
                "filled": order.get('filled', 0),
                "cost": order.get('cost', 0)
            }
            
        except Exception as e:
            logger.error(f"Order failed for user {user_id}: {e}")
            return {"success": False, "error": f"Order failed: {str(e)}"}
    
    def record_real_trade(self, user_id: int, order: Dict, symbol: str, side: str, amount: float) -> None:
        """Record real trade in database"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO real_trades 
                (user_id, exchange, symbol, side, amount, price, cost, fee, order_id, status, order_type)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                user_id,
                order.get('info', {}).get('exchange', 'unknown'),
                symbol,
                side,
                amount,
                order.get('price', 0),
                order.get('cost', 0),
                order.get('fee', {}).get('cost', 0),
                order.get('id', ''),
                order.get('status', 'unknown'),
                'market'
            ))
            
            conn.commit()

    def get_analysis_report(self, user_id: int, premium: bool = False, days: int = 30) -> Dict:
        """Genereer een analyse rapport, premium of basis"""
        if premium:
            return self.premium_analysis.analyze_performance(user_id, days)
        else:
            return self.basic_analysis.analyze_performance(user_id, days)
    
    def get_current_price(self, symbol: str) -> float:
        """Get current price for safety calculations"""
        # This should connect to your existing price fetching system
        # For now, return a default value
        return 50000.0  # Default BTC price for calculations
    
    def check_daily_limit(self, user_id: int, trade_amount_eur: float) -> bool:
        """Check if user hasn't exceeded daily trading limit"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            today = datetime.now().date()
            
            cursor.execute('''
                SELECT SUM(cost) FROM real_trades 
                WHERE user_id = ? AND DATE(timestamp) = ? AND status = 'filled'
            ''', (user_id, today))
            
            result = cursor.fetchone()
            
            daily_volume = result[0] if result[0] else 0
            
            return (daily_volume + trade_amount_eur) <= float(self.daily_trade_limit)
