#!/usr/bin/env python3
"""
Script om automatisch dependencies te installeren en analyses periodiek uit te voeren.
"""

import subprocess
import sys
import time
from datetime import datetime
from Myownmoneymaker.real_trading_system import RealTradingManager

def install_dependencies():
    try:
        import numpy
        import sklearn
        import ccxt
        print("Dependencies zijn al geïnstalleerd.")
    except ImportError:
        print("Dependencies niet gevonden, installeren...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        subprocess.check_call([sys.executable, "-m", "pip", "install", "numpy", "scikit-learn", "ccxt"])
        print("Dependencies geïnstalleerd.")

def run_analysis():
    manager = RealTradingManager()
    # Voorbeeld user_id en premium status, pas aan naar jouw situatie
    user_id = 6229184945
    premium = True

    report = manager.get_analysis_report(user_id, premium=premium, days=30)
    print(f"Analyse rapport voor user {user_id} (premium={premium}):")
    for key, value in report.items():
        print(f"  {key}: {value}")

def main():
    install_dependencies()
    # Voer analyse uit elke 24 uur (86400 seconden)
    while True:
        print(f"Start analyse: {datetime.now()}")
        run_analysis()
        print(f"Volgende analyse over 24 uur.")
        time.sleep(86400)

if __name__ == "__main__":
    main()
