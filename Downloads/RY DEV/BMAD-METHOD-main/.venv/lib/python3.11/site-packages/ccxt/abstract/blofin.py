from ccxt.base.types import Entry


class ImplicitAPI:
    public_get_market_instruments = publicGetMarketInstruments = Entry('market/instruments', 'public', 'GET', {'cost': 1})
    public_get_market_tickers = publicGetMarketTickers = Entry('market/tickers', 'public', 'GET', {'cost': 1})
    public_get_market_books = publicGetMarketBooks = Entry('market/books', 'public', 'GET', {'cost': 1})
    public_get_market_trades = publicGetMarketTrades = Entry('market/trades', 'public', 'GET', {'cost': 1})
    public_get_market_candles = publicGetMarketCandles = Entry('market/candles', 'public', 'GET', {'cost': 1})
    public_get_market_mark_price = publicGetMarketMarkPrice = Entry('market/mark-price', 'public', 'GET', {'cost': 1})
    public_get_market_funding_rate = publicGetMarketFundingRate = Entry('market/funding-rate', 'public', 'GET', {'cost': 1})
    public_get_market_funding_rate_history = publicGetMarketFundingRateHistory = Entry('market/funding-rate-history', 'public', 'GET', {'cost': 1})
    private_get_asset_balances = privateGetAssetBalances = Entry('asset/balances', 'private', 'GET', {'cost': 1})
    private_get_trade_orders_pending = privateGetTradeOrdersPending = Entry('trade/orders-pending', 'private', 'GET', {'cost': 1})
    private_get_trade_fills_history = privateGetTradeFillsHistory = Entry('trade/fills-history', 'private', 'GET', {'cost': 1})
    private_get_asset_deposit_history = privateGetAssetDepositHistory = Entry('asset/deposit-history', 'private', 'GET', {'cost': 1})
    private_get_asset_withdrawal_history = privateGetAssetWithdrawalHistory = Entry('asset/withdrawal-history', 'private', 'GET', {'cost': 1})
    private_get_asset_bills = privateGetAssetBills = Entry('asset/bills', 'private', 'GET', {'cost': 1})
    private_get_account_balance = privateGetAccountBalance = Entry('account/balance', 'private', 'GET', {'cost': 1})
    private_get_account_positions = privateGetAccountPositions = Entry('account/positions', 'private', 'GET', {'cost': 1})
    private_get_account_leverage_info = privateGetAccountLeverageInfo = Entry('account/leverage-info', 'private', 'GET', {'cost': 1})
    private_get_account_margin_mode = privateGetAccountMarginMode = Entry('account/margin-mode', 'private', 'GET', {'cost': 1})
    private_get_account_position_mode = privateGetAccountPositionMode = Entry('account/position-mode', 'private', 'GET', {'cost': 1})
    private_get_account_batch_leverage_info = privateGetAccountBatchLeverageInfo = Entry('account/batch-leverage-info', 'private', 'GET', {'cost': 1})
    private_get_trade_orders_tpsl_pending = privateGetTradeOrdersTpslPending = Entry('trade/orders-tpsl-pending', 'private', 'GET', {'cost': 1})
    private_get_trade_orders_algo_pending = privateGetTradeOrdersAlgoPending = Entry('trade/orders-algo-pending', 'private', 'GET', {'cost': 1})
    private_get_trade_orders_history = privateGetTradeOrdersHistory = Entry('trade/orders-history', 'private', 'GET', {'cost': 1})
    private_get_trade_orders_tpsl_history = privateGetTradeOrdersTpslHistory = Entry('trade/orders-tpsl-history', 'private', 'GET', {'cost': 1})
    private_get_trade_orders_algo_history = privateGetTradeOrdersAlgoHistory = Entry('trade/orders-algo-history', 'private', 'GET', {'cost': 1})
    private_get_trade_order_price_range = privateGetTradeOrderPriceRange = Entry('trade/order/price-range', 'private', 'GET', {'cost': 1})
    private_get_user_query_apikey = privateGetUserQueryApikey = Entry('user/query-apikey', 'private', 'GET', {'cost': 1})
    private_get_affiliate_basic = privateGetAffiliateBasic = Entry('affiliate/basic', 'private', 'GET', {'cost': 1})
    private_get_copytrading_instruments = privateGetCopytradingInstruments = Entry('copytrading/instruments', 'private', 'GET', {'cost': 1})
    private_get_copytrading_account_balance = privateGetCopytradingAccountBalance = Entry('copytrading/account/balance', 'private', 'GET', {'cost': 1})
    private_get_copytrading_account_positions_by_order = privateGetCopytradingAccountPositionsByOrder = Entry('copytrading/account/positions-by-order', 'private', 'GET', {'cost': 1})
    private_get_copytrading_account_positions_details_by_order = privateGetCopytradingAccountPositionsDetailsByOrder = Entry('copytrading/account/positions-details-by-order', 'private', 'GET', {'cost': 1})
    private_get_copytrading_account_positions_by_contract = privateGetCopytradingAccountPositionsByContract = Entry('copytrading/account/positions-by-contract', 'private', 'GET', {'cost': 1})
    private_get_copytrading_account_position_mode = privateGetCopytradingAccountPositionMode = Entry('copytrading/account/position-mode', 'private', 'GET', {'cost': 1})
    private_get_copytrading_account_leverage_info = privateGetCopytradingAccountLeverageInfo = Entry('copytrading/account/leverage-info', 'private', 'GET', {'cost': 1})
    private_get_copytrading_trade_orders_pending = privateGetCopytradingTradeOrdersPending = Entry('copytrading/trade/orders-pending', 'private', 'GET', {'cost': 1})
    private_get_copytrading_trade_pending_tpsl_by_contract = privateGetCopytradingTradePendingTpslByContract = Entry('copytrading/trade/pending-tpsl-by-contract', 'private', 'GET', {'cost': 1})
    private_get_copytrading_trade_position_history_by_order = privateGetCopytradingTradePositionHistoryByOrder = Entry('copytrading/trade/position-history-by-order', 'private', 'GET', {'cost': 1})
    private_get_copytrading_trade_orders_history = privateGetCopytradingTradeOrdersHistory = Entry('copytrading/trade/orders-history', 'private', 'GET', {'cost': 1})
    private_get_copytrading_trade_pending_tpsl_by_order = privateGetCopytradingTradePendingTpslByOrder = Entry('copytrading/trade/pending-tpsl-by-order', 'private', 'GET', {'cost': 1})
    private_post_account_set_margin_mode = privatePostAccountSetMarginMode = Entry('account/set-margin-mode', 'private', 'POST', {'cost': 1})
    private_post_account_set_position_mode = privatePostAccountSetPositionMode = Entry('account/set-position-mode', 'private', 'POST', {'cost': 1})
    private_post_trade_order = privatePostTradeOrder = Entry('trade/order', 'private', 'POST', {'cost': 1})
    private_post_trade_order_algo = privatePostTradeOrderAlgo = Entry('trade/order-algo', 'private', 'POST', {'cost': 1})
    private_post_trade_cancel_order = privatePostTradeCancelOrder = Entry('trade/cancel-order', 'private', 'POST', {'cost': 1})
    private_post_trade_cancel_algo = privatePostTradeCancelAlgo = Entry('trade/cancel-algo', 'private', 'POST', {'cost': 1})
    private_post_account_set_leverage = privatePostAccountSetLeverage = Entry('account/set-leverage', 'private', 'POST', {'cost': 1})
    private_post_trade_batch_orders = privatePostTradeBatchOrders = Entry('trade/batch-orders', 'private', 'POST', {'cost': 1})
    private_post_trade_order_tpsl = privatePostTradeOrderTpsl = Entry('trade/order-tpsl', 'private', 'POST', {'cost': 1})
    private_post_trade_cancel_batch_orders = privatePostTradeCancelBatchOrders = Entry('trade/cancel-batch-orders', 'private', 'POST', {'cost': 1})
    private_post_trade_cancel_tpsl = privatePostTradeCancelTpsl = Entry('trade/cancel-tpsl', 'private', 'POST', {'cost': 1})
    private_post_trade_close_position = privatePostTradeClosePosition = Entry('trade/close-position', 'private', 'POST', {'cost': 1})
    private_post_asset_transfer = privatePostAssetTransfer = Entry('asset/transfer', 'private', 'POST', {'cost': 1})
    private_post_copytrading_account_set_position_mode = privatePostCopytradingAccountSetPositionMode = Entry('copytrading/account/set-position-mode', 'private', 'POST', {'cost': 1})
    private_post_copytrading_account_set_leverage = privatePostCopytradingAccountSetLeverage = Entry('copytrading/account/set-leverage', 'private', 'POST', {'cost': 1})
    private_post_copytrading_trade_place_order = privatePostCopytradingTradePlaceOrder = Entry('copytrading/trade/place-order', 'private', 'POST', {'cost': 1})
    private_post_copytrading_trade_cancel_order = privatePostCopytradingTradeCancelOrder = Entry('copytrading/trade/cancel-order', 'private', 'POST', {'cost': 1})
    private_post_copytrading_trade_place_tpsl_by_contract = privatePostCopytradingTradePlaceTpslByContract = Entry('copytrading/trade/place-tpsl-by-contract', 'private', 'POST', {'cost': 1})
    private_post_copytrading_trade_cancel_tpsl_by_contract = privatePostCopytradingTradeCancelTpslByContract = Entry('copytrading/trade/cancel-tpsl-by-contract', 'private', 'POST', {'cost': 1})
    private_post_copytrading_trade_place_tpsl_by_order = privatePostCopytradingTradePlaceTpslByOrder = Entry('copytrading/trade/place-tpsl-by-order', 'private', 'POST', {'cost': 1})
    private_post_copytrading_trade_cancel_tpsl_by_order = privatePostCopytradingTradeCancelTpslByOrder = Entry('copytrading/trade/cancel-tpsl-by-order', 'private', 'POST', {'cost': 1})
    private_post_copytrading_trade_close_position_by_order = privatePostCopytradingTradeClosePositionByOrder = Entry('copytrading/trade/close-position-by-order', 'private', 'POST', {'cost': 1})
    private_post_copytrading_trade_close_position_by_contract = privatePostCopytradingTradeClosePositionByContract = Entry('copytrading/trade/close-position-by-contract', 'private', 'POST', {'cost': 1})
