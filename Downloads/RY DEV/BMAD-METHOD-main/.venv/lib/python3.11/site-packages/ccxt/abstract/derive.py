from ccxt.base.types import Entry


class ImplicitAPI:
    public_get_get_all_currencies = publicGetGetAllCurrencies = Entry('get_all_currencies', 'public', 'GET', {})
    public_post_build_register_session_key_tx = publicPostBuildRegisterSessionKeyTx = Entry('build_register_session_key_tx', 'public', 'POST', {})
    public_post_register_session_key = publicPostRegisterSessionKey = Entry('register_session_key', 'public', 'POST', {})
    public_post_deregister_session_key = publicPostDeregisterSessionKey = Entry('deregister_session_key', 'public', 'POST', {})
    public_post_login = publicPostLogin = Entry('login', 'public', 'POST', {})
    public_post_statistics = publicPostStatistics = Entry('statistics', 'public', 'POST', {})
    public_post_get_all_currencies = publicPostGetAllCurrencies = Entry('get_all_currencies', 'public', 'POST', {})
    public_post_get_currency = publicPostGetCurrency = Entry('get_currency', 'public', 'POST', {})
    public_post_get_instrument = publicPostGetInstrument = Entry('get_instrument', 'public', 'POST', {})
    public_post_get_all_instruments = publicPostGetAllInstruments = Entry('get_all_instruments', 'public', 'POST', {})
    public_post_get_instruments = publicPostGetInstruments = Entry('get_instruments', 'public', 'POST', {})
    public_post_get_ticker = publicPostGetTicker = Entry('get_ticker', 'public', 'POST', {})
    public_post_get_latest_signed_feeds = publicPostGetLatestSignedFeeds = Entry('get_latest_signed_feeds', 'public', 'POST', {})
    public_post_get_option_settlement_prices = publicPostGetOptionSettlementPrices = Entry('get_option_settlement_prices', 'public', 'POST', {})
    public_post_get_spot_feed_history = publicPostGetSpotFeedHistory = Entry('get_spot_feed_history', 'public', 'POST', {})
    public_post_get_spot_feed_history_candles = publicPostGetSpotFeedHistoryCandles = Entry('get_spot_feed_history_candles', 'public', 'POST', {})
    public_post_get_funding_rate_history = publicPostGetFundingRateHistory = Entry('get_funding_rate_history', 'public', 'POST', {})
    public_post_get_trade_history = publicPostGetTradeHistory = Entry('get_trade_history', 'public', 'POST', {})
    public_post_get_option_settlement_history = publicPostGetOptionSettlementHistory = Entry('get_option_settlement_history', 'public', 'POST', {})
    public_post_get_liquidation_history = publicPostGetLiquidationHistory = Entry('get_liquidation_history', 'public', 'POST', {})
    public_post_get_interest_rate_history = publicPostGetInterestRateHistory = Entry('get_interest_rate_history', 'public', 'POST', {})
    public_post_get_transaction = publicPostGetTransaction = Entry('get_transaction', 'public', 'POST', {})
    public_post_get_margin = publicPostGetMargin = Entry('get_margin', 'public', 'POST', {})
    public_post_margin_watch = publicPostMarginWatch = Entry('margin_watch', 'public', 'POST', {})
    public_post_validate_invite_code = publicPostValidateInviteCode = Entry('validate_invite_code', 'public', 'POST', {})
    public_post_get_points = publicPostGetPoints = Entry('get_points', 'public', 'POST', {})
    public_post_get_all_points = publicPostGetAllPoints = Entry('get_all_points', 'public', 'POST', {})
    public_post_get_points_leaderboard = publicPostGetPointsLeaderboard = Entry('get_points_leaderboard', 'public', 'POST', {})
    public_post_get_descendant_tree = publicPostGetDescendantTree = Entry('get_descendant_tree', 'public', 'POST', {})
    public_post_get_tree_roots = publicPostGetTreeRoots = Entry('get_tree_roots', 'public', 'POST', {})
    public_post_get_swell_percent_points = publicPostGetSwellPercentPoints = Entry('get_swell_percent_points', 'public', 'POST', {})
    public_post_get_vault_assets = publicPostGetVaultAssets = Entry('get_vault_assets', 'public', 'POST', {})
    public_post_get_etherfi_effective_balances = publicPostGetEtherfiEffectiveBalances = Entry('get_etherfi_effective_balances', 'public', 'POST', {})
    public_post_get_kelp_effective_balances = publicPostGetKelpEffectiveBalances = Entry('get_kelp_effective_balances', 'public', 'POST', {})
    public_post_get_bridge_balances = publicPostGetBridgeBalances = Entry('get_bridge_balances', 'public', 'POST', {})
    public_post_get_ethena_participants = publicPostGetEthenaParticipants = Entry('get_ethena_participants', 'public', 'POST', {})
    public_post_get_vault_share = publicPostGetVaultShare = Entry('get_vault_share', 'public', 'POST', {})
    public_post_get_vault_statistics = publicPostGetVaultStatistics = Entry('get_vault_statistics', 'public', 'POST', {})
    public_post_get_vault_balances = publicPostGetVaultBalances = Entry('get_vault_balances', 'public', 'POST', {})
    public_post_estimate_integrator_points = publicPostEstimateIntegratorPoints = Entry('estimate_integrator_points', 'public', 'POST', {})
    public_post_create_subaccount_debug = publicPostCreateSubaccountDebug = Entry('create_subaccount_debug', 'public', 'POST', {})
    public_post_deposit_debug = publicPostDepositDebug = Entry('deposit_debug', 'public', 'POST', {})
    public_post_withdraw_debug = publicPostWithdrawDebug = Entry('withdraw_debug', 'public', 'POST', {})
    public_post_send_quote_debug = publicPostSendQuoteDebug = Entry('send_quote_debug', 'public', 'POST', {})
    public_post_execute_quote_debug = publicPostExecuteQuoteDebug = Entry('execute_quote_debug', 'public', 'POST', {})
    public_post_get_invite_code = publicPostGetInviteCode = Entry('get_invite_code', 'public', 'POST', {})
    public_post_register_invite = publicPostRegisterInvite = Entry('register_invite', 'public', 'POST', {})
    public_post_get_time = publicPostGetTime = Entry('get_time', 'public', 'POST', {})
    public_post_get_live_incidents = publicPostGetLiveIncidents = Entry('get_live_incidents', 'public', 'POST', {})
    public_post_get_maker_programs = publicPostGetMakerPrograms = Entry('get_maker_programs', 'public', 'POST', {})
    public_post_get_maker_program_scores = publicPostGetMakerProgramScores = Entry('get_maker_program_scores', 'public', 'POST', {})
    private_post_get_account = privatePostGetAccount = Entry('get_account', 'private', 'POST', {})
    private_post_create_subaccount = privatePostCreateSubaccount = Entry('create_subaccount', 'private', 'POST', {})
    private_post_get_subaccount = privatePostGetSubaccount = Entry('get_subaccount', 'private', 'POST', {})
    private_post_get_subaccounts = privatePostGetSubaccounts = Entry('get_subaccounts', 'private', 'POST', {})
    private_post_get_all_portfolios = privatePostGetAllPortfolios = Entry('get_all_portfolios', 'private', 'POST', {})
    private_post_change_subaccount_label = privatePostChangeSubaccountLabel = Entry('change_subaccount_label', 'private', 'POST', {})
    private_post_get_notificationsv = privatePostGetNotificationsv = Entry('get_notificationsv', 'private', 'POST', {})
    private_post_update_notifications = privatePostUpdateNotifications = Entry('update_notifications', 'private', 'POST', {})
    private_post_deposit = privatePostDeposit = Entry('deposit', 'private', 'POST', {})
    private_post_withdraw = privatePostWithdraw = Entry('withdraw', 'private', 'POST', {})
    private_post_transfer_erc20 = privatePostTransferErc20 = Entry('transfer_erc20', 'private', 'POST', {})
    private_post_transfer_position = privatePostTransferPosition = Entry('transfer_position', 'private', 'POST', {})
    private_post_transfer_positions = privatePostTransferPositions = Entry('transfer_positions', 'private', 'POST', {})
    private_post_order = privatePostOrder = Entry('order', 'private', 'POST', {})
    private_post_replace = privatePostReplace = Entry('replace', 'private', 'POST', {})
    private_post_order_debug = privatePostOrderDebug = Entry('order_debug', 'private', 'POST', {})
    private_post_get_order = privatePostGetOrder = Entry('get_order', 'private', 'POST', {})
    private_post_get_orders = privatePostGetOrders = Entry('get_orders', 'private', 'POST', {})
    private_post_get_open_orders = privatePostGetOpenOrders = Entry('get_open_orders', 'private', 'POST', {})
    private_post_cancel = privatePostCancel = Entry('cancel', 'private', 'POST', {})
    private_post_cancel_by_label = privatePostCancelByLabel = Entry('cancel_by_label', 'private', 'POST', {})
    private_post_cancel_by_nonce = privatePostCancelByNonce = Entry('cancel_by_nonce', 'private', 'POST', {})
    private_post_cancel_by_instrument = privatePostCancelByInstrument = Entry('cancel_by_instrument', 'private', 'POST', {})
    private_post_cancel_all = privatePostCancelAll = Entry('cancel_all', 'private', 'POST', {})
    private_post_cancel_trigger_order = privatePostCancelTriggerOrder = Entry('cancel_trigger_order', 'private', 'POST', {})
    private_post_get_order_history = privatePostGetOrderHistory = Entry('get_order_history', 'private', 'POST', {})
    private_post_get_trade_history = privatePostGetTradeHistory = Entry('get_trade_history', 'private', 'POST', {})
    private_post_get_deposit_history = privatePostGetDepositHistory = Entry('get_deposit_history', 'private', 'POST', {})
    private_post_get_withdrawal_history = privatePostGetWithdrawalHistory = Entry('get_withdrawal_history', 'private', 'POST', {})
    private_post_send_rfq = privatePostSendRfq = Entry('send_rfq', 'private', 'POST', {})
    private_post_cancel_rfq = privatePostCancelRfq = Entry('cancel_rfq', 'private', 'POST', {})
    private_post_cancel_batch_rfqs = privatePostCancelBatchRfqs = Entry('cancel_batch_rfqs', 'private', 'POST', {})
    private_post_get_rfqs = privatePostGetRfqs = Entry('get_rfqs', 'private', 'POST', {})
    private_post_poll_rfqs = privatePostPollRfqs = Entry('poll_rfqs', 'private', 'POST', {})
    private_post_send_quote = privatePostSendQuote = Entry('send_quote', 'private', 'POST', {})
    private_post_cancel_quote = privatePostCancelQuote = Entry('cancel_quote', 'private', 'POST', {})
    private_post_cancel_batch_quotes = privatePostCancelBatchQuotes = Entry('cancel_batch_quotes', 'private', 'POST', {})
    private_post_get_quotes = privatePostGetQuotes = Entry('get_quotes', 'private', 'POST', {})
    private_post_poll_quotes = privatePostPollQuotes = Entry('poll_quotes', 'private', 'POST', {})
    private_post_execute_quote = privatePostExecuteQuote = Entry('execute_quote', 'private', 'POST', {})
    private_post_rfq_get_best_quote = privatePostRfqGetBestQuote = Entry('rfq_get_best_quote', 'private', 'POST', {})
    private_post_get_margin = privatePostGetMargin = Entry('get_margin', 'private', 'POST', {})
    private_post_get_collaterals = privatePostGetCollaterals = Entry('get_collaterals', 'private', 'POST', {})
    private_post_get_positions = privatePostGetPositions = Entry('get_positions', 'private', 'POST', {})
    private_post_get_option_settlement_history = privatePostGetOptionSettlementHistory = Entry('get_option_settlement_history', 'private', 'POST', {})
    private_post_get_subaccount_value_history = privatePostGetSubaccountValueHistory = Entry('get_subaccount_value_history', 'private', 'POST', {})
    private_post_expired_and_cancelled_history = privatePostExpiredAndCancelledHistory = Entry('expired_and_cancelled_history', 'private', 'POST', {})
    private_post_get_funding_history = privatePostGetFundingHistory = Entry('get_funding_history', 'private', 'POST', {})
    private_post_get_interest_history = privatePostGetInterestHistory = Entry('get_interest_history', 'private', 'POST', {})
    private_post_get_erc20_transfer_history = privatePostGetErc20TransferHistory = Entry('get_erc20_transfer_history', 'private', 'POST', {})
    private_post_get_liquidation_history = privatePostGetLiquidationHistory = Entry('get_liquidation_history', 'private', 'POST', {})
    private_post_liquidate = privatePostLiquidate = Entry('liquidate', 'private', 'POST', {})
    private_post_get_liquidator_history = privatePostGetLiquidatorHistory = Entry('get_liquidator_history', 'private', 'POST', {})
    private_post_session_keys = privatePostSessionKeys = Entry('session_keys', 'private', 'POST', {})
    private_post_edit_session_key = privatePostEditSessionKey = Entry('edit_session_key', 'private', 'POST', {})
    private_post_register_scoped_session_key = privatePostRegisterScopedSessionKey = Entry('register_scoped_session_key', 'private', 'POST', {})
    private_post_get_mmp_config = privatePostGetMmpConfig = Entry('get_mmp_config', 'private', 'POST', {})
    private_post_set_mmp_config = privatePostSetMmpConfig = Entry('set_mmp_config', 'private', 'POST', {})
    private_post_reset_mmp = privatePostResetMmp = Entry('reset_mmp', 'private', 'POST', {})
    private_post_set_cancel_on_disconnect = privatePostSetCancelOnDisconnect = Entry('set_cancel_on_disconnect', 'private', 'POST', {})
    private_post_get_invite_code = privatePostGetInviteCode = Entry('get_invite_code', 'private', 'POST', {})
    private_post_register_invite = privatePostRegisterInvite = Entry('register_invite', 'private', 'POST', {})
