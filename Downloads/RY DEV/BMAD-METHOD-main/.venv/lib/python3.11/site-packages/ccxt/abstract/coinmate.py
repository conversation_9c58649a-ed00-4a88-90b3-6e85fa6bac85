from ccxt.base.types import Entry


class ImplicitAPI:
    public_get_orderbook = publicGetOrderBook = Entry('orderBook', 'public', 'GET', {})
    public_get_ticker = publicGetTicker = Entry('ticker', 'public', 'GET', {})
    public_get_tickerall = publicGetTickerAll = Entry('tickerAll', 'public', 'GET', {})
    public_get_products = publicGetProducts = Entry('products', 'public', 'GET', {})
    public_get_transactions = publicGetTransactions = Entry('transactions', 'public', 'GET', {})
    public_get_tradingpairs = publicGetTradingPairs = Entry('tradingPairs', 'public', 'GET', {})
    private_post_balances = privatePostBalances = Entry('balances', 'private', 'POST', {})
    private_post_bitcoincashwithdrawal = privatePostBitcoinCashWithdrawal = Entry('bitcoinCashWithdrawal', 'private', 'POST', {})
    private_post_bitcoincashdepositaddresses = privatePostBitcoinCashDepositAddresses = Entry('bitcoinCashDepositAddresses', 'private', 'POST', {})
    private_post_bitcoindepositaddresses = privatePostBitcoinDepositAddresses = Entry('bitcoinDepositAddresses', 'private', 'POST', {})
    private_post_bitcoinwithdrawal = privatePostBitcoinWithdrawal = Entry('bitcoinWithdrawal', 'private', 'POST', {})
    private_post_bitcoinwithdrawalfees = privatePostBitcoinWithdrawalFees = Entry('bitcoinWithdrawalFees', 'private', 'POST', {})
    private_post_buyinstant = privatePostBuyInstant = Entry('buyInstant', 'private', 'POST', {})
    private_post_buylimit = privatePostBuyLimit = Entry('buyLimit', 'private', 'POST', {})
    private_post_cancelorder = privatePostCancelOrder = Entry('cancelOrder', 'private', 'POST', {})
    private_post_cancelorderwithinfo = privatePostCancelOrderWithInfo = Entry('cancelOrderWithInfo', 'private', 'POST', {})
    private_post_createvoucher = privatePostCreateVoucher = Entry('createVoucher', 'private', 'POST', {})
    private_post_dashdepositaddresses = privatePostDashDepositAddresses = Entry('dashDepositAddresses', 'private', 'POST', {})
    private_post_dashwithdrawal = privatePostDashWithdrawal = Entry('dashWithdrawal', 'private', 'POST', {})
    private_post_ethereumwithdrawal = privatePostEthereumWithdrawal = Entry('ethereumWithdrawal', 'private', 'POST', {})
    private_post_ethereumdepositaddresses = privatePostEthereumDepositAddresses = Entry('ethereumDepositAddresses', 'private', 'POST', {})
    private_post_litecoinwithdrawal = privatePostLitecoinWithdrawal = Entry('litecoinWithdrawal', 'private', 'POST', {})
    private_post_litecoindepositaddresses = privatePostLitecoinDepositAddresses = Entry('litecoinDepositAddresses', 'private', 'POST', {})
    private_post_openorders = privatePostOpenOrders = Entry('openOrders', 'private', 'POST', {})
    private_post_order = privatePostOrder = Entry('order', 'private', 'POST', {})
    private_post_orderhistory = privatePostOrderHistory = Entry('orderHistory', 'private', 'POST', {})
    private_post_orderbyid = privatePostOrderById = Entry('orderById', 'private', 'POST', {})
    private_post_pusherauth = privatePostPusherAuth = Entry('pusherAuth', 'private', 'POST', {})
    private_post_redeemvoucher = privatePostRedeemVoucher = Entry('redeemVoucher', 'private', 'POST', {})
    private_post_replacebybuylimit = privatePostReplaceByBuyLimit = Entry('replaceByBuyLimit', 'private', 'POST', {})
    private_post_replacebybuyinstant = privatePostReplaceByBuyInstant = Entry('replaceByBuyInstant', 'private', 'POST', {})
    private_post_replacebyselllimit = privatePostReplaceBySellLimit = Entry('replaceBySellLimit', 'private', 'POST', {})
    private_post_replacebysellinstant = privatePostReplaceBySellInstant = Entry('replaceBySellInstant', 'private', 'POST', {})
    private_post_rippledepositaddresses = privatePostRippleDepositAddresses = Entry('rippleDepositAddresses', 'private', 'POST', {})
    private_post_ripplewithdrawal = privatePostRippleWithdrawal = Entry('rippleWithdrawal', 'private', 'POST', {})
    private_post_sellinstant = privatePostSellInstant = Entry('sellInstant', 'private', 'POST', {})
    private_post_selllimit = privatePostSellLimit = Entry('sellLimit', 'private', 'POST', {})
    private_post_transactionhistory = privatePostTransactionHistory = Entry('transactionHistory', 'private', 'POST', {})
    private_post_traderfees = privatePostTraderFees = Entry('traderFees', 'private', 'POST', {})
    private_post_tradehistory = privatePostTradeHistory = Entry('tradeHistory', 'private', 'POST', {})
    private_post_transfer = privatePostTransfer = Entry('transfer', 'private', 'POST', {})
    private_post_transferhistory = privatePostTransferHistory = Entry('transferHistory', 'private', 'POST', {})
    private_post_unconfirmedbitcoindeposits = privatePostUnconfirmedBitcoinDeposits = Entry('unconfirmedBitcoinDeposits', 'private', 'POST', {})
    private_post_unconfirmedbitcoincashdeposits = privatePostUnconfirmedBitcoinCashDeposits = Entry('unconfirmedBitcoinCashDeposits', 'private', 'POST', {})
    private_post_unconfirmeddashdeposits = privatePostUnconfirmedDashDeposits = Entry('unconfirmedDashDeposits', 'private', 'POST', {})
    private_post_unconfirmedethereumdeposits = privatePostUnconfirmedEthereumDeposits = Entry('unconfirmedEthereumDeposits', 'private', 'POST', {})
    private_post_unconfirmedlitecoindeposits = privatePostUnconfirmedLitecoinDeposits = Entry('unconfirmedLitecoinDeposits', 'private', 'POST', {})
    private_post_unconfirmedrippledeposits = privatePostUnconfirmedRippleDeposits = Entry('unconfirmedRippleDeposits', 'private', 'POST', {})
    private_post_cancelallopenorders = privatePostCancelAllOpenOrders = Entry('cancelAllOpenOrders', 'private', 'POST', {})
    private_post_withdrawvirtualcurrency = privatePostWithdrawVirtualCurrency = Entry('withdrawVirtualCurrency', 'private', 'POST', {})
    private_post_virtualcurrencydepositaddresses = privatePostVirtualCurrencyDepositAddresses = Entry('virtualCurrencyDepositAddresses', 'private', 'POST', {})
    private_post_unconfirmedvirtualcurrencydeposits = privatePostUnconfirmedVirtualCurrencyDeposits = Entry('unconfirmedVirtualCurrencyDeposits', 'private', 'POST', {})
    private_post_adawithdrawal = privatePostAdaWithdrawal = Entry('adaWithdrawal', 'private', 'POST', {})
    private_post_adadepositaddresses = privatePostAdaDepositAddresses = Entry('adaDepositAddresses', 'private', 'POST', {})
    private_post_unconfirmedadadeposits = privatePostUnconfirmedAdaDeposits = Entry('unconfirmedAdaDeposits', 'private', 'POST', {})
    private_post_solwithdrawal = privatePostSolWithdrawal = Entry('solWithdrawal', 'private', 'POST', {})
    private_post_soldepositaddresses = privatePostSolDepositAddresses = Entry('solDepositAddresses', 'private', 'POST', {})
    private_post_unconfirmedsoldeposits = privatePostUnconfirmedSolDeposits = Entry('unconfirmedSolDeposits', 'private', 'POST', {})
