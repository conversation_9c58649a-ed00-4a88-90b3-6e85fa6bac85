# -*- coding: utf-8 -*-

# PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
# https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

from ccxt.async_support.coinbase import coinbase
from ccxt.abstract.coinbaseadvanced import ImplicitAPI
from ccxt.base.types import Any


class coinbaseadvanced(coinbase, ImplicitAPI):

    def describe(self) -> Any:
        return self.deep_extend(super(coinbaseadvanced, self).describe(), {
            'id': 'coinbaseadvanced',
            'name': 'Coinbase Advanced',
            'alias': True,
        })
