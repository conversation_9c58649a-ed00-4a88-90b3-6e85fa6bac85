from ccxt.base.types import Entry


class ImplicitAPI:
    public_get_demo_temp = publicGetDemoTemp = Entry('demo/temp', 'public', 'GET', {'cost': 1})
    public_get_exchange_candles_pair_timeframe_from_to = publicGetExchangeCandlesPairTimeframeFromTo = Entry('exchange/candles/{pair}/{timeframe}/{from}/{to}', 'public', 'GET', {'cost': 3})
    public_get_exchange_prices = publicGetExchangePrices = Entry('exchange/prices', 'public', 'GET', {'cost': 1})
    public_get_exchange_ticks_pair_from = publicGetExchangeTicksPairFrom = Entry('exchange/ticks/{pair}/{from}', 'public', 'GET', {'cost': 3})
    public_get_assets = publicGetAssets = Entry('assets', 'public', 'GET', {'cost': 1})
    public_get_markets = publicGetMarkets = Entry('markets', 'public', 'GET', {'cost': 1})
    public_get_exchange_book_pair = publicGetExchangeBookPair = Entry('exchange/book/{pair}', 'public', 'GET', {'cost': 3})
    public_get_exchange_bookupdates_pair_from = publicGetExchangeBookUpdatesPairFrom = Entry('exchange/bookUpdates/{pair}/{from}', 'public', 'GET', {'cost': 1})
    private_get_users_balances = privateGetUsersBalances = Entry('users/balances', 'private', 'GET', {'cost': 1})
    private_get_users_wallets = privateGetUsersWallets = Entry('users/wallets', 'private', 'GET', {'cost': 1})
    private_get_users_wallets_history_since = privateGetUsersWalletsHistorySince = Entry('users/wallets/history/{since}', 'private', 'GET', {'cost': 1.67})
    private_get_exchange_orders_status_orderid = privateGetExchangeOrdersStatusOrderID = Entry('exchange/orders/status/{orderID}', 'private', 'GET', {'cost': 1})
    private_get_exchange_orders_active = privateGetExchangeOrdersActive = Entry('exchange/orders/active', 'private', 'GET', {'cost': 1})
    private_get_exchange_orders_history_since = privateGetExchangeOrdersHistorySince = Entry('exchange/orders/history/{since}', 'private', 'GET', {'cost': 1.67})
    private_get_exchange_fills_since = privateGetExchangeFillsSince = Entry('exchange/fills/{since}', 'private', 'GET', {'cost': 1.67})
    private_get_exchange_margin = privateGetExchangeMargin = Entry('exchange/margin', 'private', 'GET', {'cost': 1})
    private_post_jwt = privatePostJwt = Entry('jwt', 'private', 'POST', {'cost': 1})
    private_post_jwtdevice = privatePostJwtDevice = Entry('jwtDevice', 'private', 'POST', {'cost': 1})
    private_post_devices = privatePostDevices = Entry('devices', 'private', 'POST', {'cost': 1})
    private_post_jwt_read_only = privatePostJwtReadOnly = Entry('jwt-read-only', 'private', 'POST', {'cost': 1})
    private_post_exchange_orders_create = privatePostExchangeOrdersCreate = Entry('exchange/orders/create', 'private', 'POST', {'cost': 1})
    private_post_exchange_orders_modify_orderid = privatePostExchangeOrdersModifyOrderID = Entry('exchange/orders/modify/{orderID}', 'private', 'POST', {'cost': 1})
    private_post_exchange_swap = privatePostExchangeSwap = Entry('exchange/swap', 'private', 'POST', {'cost': 1})
    private_post_exchange_swap_confirm_swapid = privatePostExchangeSwapConfirmSwapId = Entry('exchange/swap/confirm/{swapId}', 'private', 'POST', {'cost': 1})
    private_post_exchange_orders_close_orderid = privatePostExchangeOrdersCloseOrderID = Entry('exchange/orders/close/{orderID}', 'private', 'POST', {'cost': 1})
    private_post_exchange_orders_hedge = privatePostExchangeOrdersHedge = Entry('exchange/orders/hedge', 'private', 'POST', {'cost': 1})
    private_put_jwt = privatePutJwt = Entry('jwt', 'private', 'PUT', {'cost': 1})
    private_put_exchange_orders_cancel_orderid = privatePutExchangeOrdersCancelOrderID = Entry('exchange/orders/cancel/{orderID}', 'private', 'PUT', {'cost': 1})
    private_put_users_margin_collateral = privatePutUsersMarginCollateral = Entry('users/margin/collateral', 'private', 'PUT', {'cost': 1})
    private_put_users_margin_primary_currency = privatePutUsersMarginPrimaryCurrency = Entry('users/margin/primary/{currency}', 'private', 'PUT', {'cost': 1})
