from ccxt.base.types import Entry


class ImplicitAPI:
    public_get_api_spot_v1_public_time = publicGetApiSpotV1PublicTime = Entry('api/spot/v1/public/time', 'public', 'GET', {'cost': 1})
    public_get_api_spot_v1_public_currencies = publicGetApiSpotV1PublicCurrencies = Entry('api/spot/v1/public/currencies', 'public', 'GET', {'cost': 6.666666666666667})
    public_get_api_spot_v1_market_ticker = publicGetApiSpotV1MarketTicker = Entry('api/spot/v1/market/ticker', 'public', 'GET', {'cost': 1})
    public_get_api_spot_v1_market_tickers = publicGetApiSpotV1MarketTickers = Entry('api/spot/v1/market/tickers', 'public', 'GET', {'cost': 1})
    public_get_api_spot_v1_market_fills = publicGetApiSpotV1MarketFills = Entry('api/spot/v1/market/fills', 'public', 'GET', {'cost': 2})
    public_get_api_spot_v1_market_fills_history = publicGetApiSpotV1MarketFillsHistory = Entry('api/spot/v1/market/fills-history', 'public', 'GET', {'cost': 2})
    public_get_api_spot_v1_market_candles = publicGetApiSpotV1MarketCandles = Entry('api/spot/v1/market/candles', 'public', 'GET', {'cost': 1})
    public_get_api_spot_v1_market_history_candles = publicGetApiSpotV1MarketHistoryCandles = Entry('api/spot/v1/market/history-candles', 'public', 'GET', {'cost': 1})
    public_get_api_spot_v1_market_depth = publicGetApiSpotV1MarketDepth = Entry('api/spot/v1/market/depth', 'public', 'GET', {'cost': 1})
    public_get_api_spot_v1_market_merge_depth = publicGetApiSpotV1MarketMergeDepth = Entry('api/spot/v1/market/merge-depth', 'public', 'GET', {'cost': 1})
    public_get_api_mix_v1_market_contracts = publicGetApiMixV1MarketContracts = Entry('api/mix/v1/market/contracts', 'public', 'GET', {'cost': 1})
    public_get_api_mix_v1_market_merge_depth = publicGetApiMixV1MarketMergeDepth = Entry('api/mix/v1/market/merge-depth', 'public', 'GET', {'cost': 1})
    public_get_api_mix_v1_market_depth = publicGetApiMixV1MarketDepth = Entry('api/mix/v1/market/depth', 'public', 'GET', {'cost': 1})
    public_get_api_mix_v1_market_ticker = publicGetApiMixV1MarketTicker = Entry('api/mix/v1/market/ticker', 'public', 'GET', {'cost': 1})
    public_get_api_mix_v1_market_tickers = publicGetApiMixV1MarketTickers = Entry('api/mix/v1/market/tickers', 'public', 'GET', {'cost': 1})
    public_get_api_mix_v1_market_fills = publicGetApiMixV1MarketFills = Entry('api/mix/v1/market/fills', 'public', 'GET', {'cost': 1})
    public_get_api_mix_v1_market_fills_history = publicGetApiMixV1MarketFillsHistory = Entry('api/mix/v1/market/fills-history', 'public', 'GET', {'cost': 1})
    public_get_api_mix_v1_market_candles = publicGetApiMixV1MarketCandles = Entry('api/mix/v1/market/candles', 'public', 'GET', {'cost': 1})
    public_get_pi_mix_v1_market_index = publicGetPiMixV1MarketIndex = Entry('pi/mix/v1/market/index', 'public', 'GET', {'cost': 1})
    public_get_api_mix_v1_market_funding_time = publicGetApiMixV1MarketFundingTime = Entry('api/mix/v1/market/funding-time', 'public', 'GET', {'cost': 1})
    public_get_api_mix_v1_market_history_fundrate = publicGetApiMixV1MarketHistoryFundRate = Entry('api/mix/v1/market/history-fundRate', 'public', 'GET', {'cost': 1})
    public_get_api_mix_v1_market_current_fundrate = publicGetApiMixV1MarketCurrentFundRate = Entry('api/mix/v1/market/current-fundRate', 'public', 'GET', {'cost': 1})
    public_get_api_mix_v1_market_open_interest = publicGetApiMixV1MarketOpenInterest = Entry('api/mix/v1/market/open-interest', 'public', 'GET', {'cost': 1})
    public_get_api_mix_v1_market_mark_price = publicGetApiMixV1MarketMarkPrice = Entry('api/mix/v1/market/mark-price', 'public', 'GET', {'cost': 1})
    public_get_api_mix_v1_market_symbol_leverage = publicGetApiMixV1MarketSymbolLeverage = Entry('api/mix/v1/market/symbol-leverage', 'public', 'GET', {'cost': 1})
    public_get_api_mix_v1_market_querypositionlever = publicGetApiMixV1MarketQueryPositionLever = Entry('api/mix/v1/market/queryPositionLever', 'public', 'GET', {'cost': 1})
    private_get_api_spot_v1_wallet_deposit_address = privateGetApiSpotV1WalletDepositAddress = Entry('api/spot/v1/wallet/deposit-address', 'private', 'GET', {'cost': 4})
    private_get_pi_spot_v1_wallet_withdrawal_list = privateGetPiSpotV1WalletWithdrawalList = Entry('pi/spot/v1/wallet/withdrawal-list', 'private', 'GET', {'cost': 1})
    private_get_api_spot_v1_wallet_withdrawal_list_v2 = privateGetApiSpotV1WalletWithdrawalListV2 = Entry('api/spot/v1/wallet/withdrawal-list-v2', 'private', 'GET', {'cost': 1})
    private_get_api_spot_v1_wallet_deposit_list = privateGetApiSpotV1WalletDepositList = Entry('api/spot/v1/wallet/deposit-list', 'private', 'GET', {'cost': 1})
    private_get_api_spot_v1_account_getinfo = privateGetApiSpotV1AccountGetInfo = Entry('api/spot/v1/account/getInfo', 'private', 'GET', {'cost': 1})
    private_get_api_spot_v1_account_assets = privateGetApiSpotV1AccountAssets = Entry('api/spot/v1/account/assets', 'private', 'GET', {'cost': 2})
    private_get_api_spot_v1_account_transferrecords = privateGetApiSpotV1AccountTransferRecords = Entry('api/spot/v1/account/transferRecords', 'private', 'GET', {'cost': 1})
    private_get_api_mix_v1_account_account = privateGetApiMixV1AccountAccount = Entry('api/mix/v1/account/account', 'private', 'GET', {'cost': 2})
    private_get_api_mix_v1_account_accounts = privateGetApiMixV1AccountAccounts = Entry('api/mix/v1/account/accounts', 'private', 'GET', {'cost': 2})
    private_get_api_mix_v1_position_singleposition_v2 = privateGetApiMixV1PositionSinglePositionV2 = Entry('api/mix/v1/position/singlePosition-v2', 'private', 'GET', {'cost': 2})
    private_get_api_mix_v1_position_allposition_v2 = privateGetApiMixV1PositionAllPositionV2 = Entry('api/mix/v1/position/allPosition-v2', 'private', 'GET', {'cost': 4})
    private_get_api_mix_v1_account_accountbill = privateGetApiMixV1AccountAccountBill = Entry('api/mix/v1/account/accountBill', 'private', 'GET', {'cost': 2})
    private_get_api_mix_v1_account_accountbusinessbill = privateGetApiMixV1AccountAccountBusinessBill = Entry('api/mix/v1/account/accountBusinessBill', 'private', 'GET', {'cost': 4})
    private_get_api_mix_v1_order_current = privateGetApiMixV1OrderCurrent = Entry('api/mix/v1/order/current', 'private', 'GET', {'cost': 1})
    private_get_api_mix_v1_order_margincoincurrent = privateGetApiMixV1OrderMarginCoinCurrent = Entry('api/mix/v1/order/marginCoinCurrent', 'private', 'GET', {'cost': 1})
    private_get_api_mix_v1_order_history = privateGetApiMixV1OrderHistory = Entry('api/mix/v1/order/history', 'private', 'GET', {'cost': 2})
    private_get_api_mix_v1_order_historyproducttype = privateGetApiMixV1OrderHistoryProductType = Entry('api/mix/v1/order/historyProductType', 'private', 'GET', {'cost': 4})
    private_get_api_mix_v1_order_detail = privateGetApiMixV1OrderDetail = Entry('api/mix/v1/order/detail', 'private', 'GET', {'cost': 2})
    private_get_api_mix_v1_order_fills = privateGetApiMixV1OrderFills = Entry('api/mix/v1/order/fills', 'private', 'GET', {'cost': 2})
    private_get_api_mix_v1_order_allfills = privateGetApiMixV1OrderAllFills = Entry('api/mix/v1/order/allFills', 'private', 'GET', {'cost': 2})
    private_get_api_mix_v1_plan_currentplan = privateGetApiMixV1PlanCurrentPlan = Entry('api/mix/v1/plan/currentPlan', 'private', 'GET', {'cost': 1})
    private_get_api_mix_v1_plan_historyplan = privateGetApiMixV1PlanHistoryPlan = Entry('api/mix/v1/plan/historyPlan', 'private', 'GET', {'cost': 2})
    private_post_api_spot_v1_wallet_transfer_v2 = privatePostApiSpotV1WalletTransferV2 = Entry('api/spot/v1/wallet/transfer-v2', 'private', 'POST', {'cost': 4})
    private_post_api_spot_v1_wallet_withdrawal_v2 = privatePostApiSpotV1WalletWithdrawalV2 = Entry('api/spot/v1/wallet/withdrawal-v2', 'private', 'POST', {'cost': 4})
    private_post_api_spot_v1_wallet_withdrawal_inner_v2 = privatePostApiSpotV1WalletWithdrawalInnerV2 = Entry('api/spot/v1/wallet/withdrawal-inner-v2', 'private', 'POST', {'cost': 1})
    private_post_api_spot_v1_account_bills = privatePostApiSpotV1AccountBills = Entry('api/spot/v1/account/bills', 'private', 'POST', {'cost': 2})
    private_post_api_spot_v1_trade_orders = privatePostApiSpotV1TradeOrders = Entry('api/spot/v1/trade/orders', 'private', 'POST', {'cost': 2})
    private_post_api_spot_v1_trade_batch_orders = privatePostApiSpotV1TradeBatchOrders = Entry('api/spot/v1/trade/batch-orders', 'private', 'POST', {'cost': 4, 'step': 10})
    private_post_api_spot_v1_trade_cancel_order = privatePostApiSpotV1TradeCancelOrder = Entry('api/spot/v1/trade/cancel-order', 'private', 'POST', {'cost': 1})
    private_post_api_spot_v1_trade_cancel_order_v2 = privatePostApiSpotV1TradeCancelOrderV2 = Entry('api/spot/v1/trade/cancel-order-v2', 'private', 'POST', {'cost': 2})
    private_post_api_spot_v1_trade_cancel_symbol_order = privatePostApiSpotV1TradeCancelSymbolOrder = Entry('api/spot/v1/trade/cancel-symbol-order', 'private', 'POST', {'cost': 2})
    private_post_api_spot_v1_trade_cancel_batch_orders = privatePostApiSpotV1TradeCancelBatchOrders = Entry('api/spot/v1/trade/cancel-batch-orders', 'private', 'POST', {'cost': 1})
    private_post_api_spot_v1_trade_cancel_batch_orders_v2 = privatePostApiSpotV1TradeCancelBatchOrdersV2 = Entry('api/spot/v1/trade/cancel-batch-orders-v2', 'private', 'POST', {'cost': 1})
    private_post_api_spot_v1_trade_orderinfo = privatePostApiSpotV1TradeOrderInfo = Entry('api/spot/v1/trade/orderInfo', 'private', 'POST', {'cost': 1})
    private_post_api_spot_v1_trade_open_orders = privatePostApiSpotV1TradeOpenOrders = Entry('api/spot/v1/trade/open-orders', 'private', 'POST', {'cost': 1})
    private_post_api_spot_v1_trade_history = privatePostApiSpotV1TradeHistory = Entry('api/spot/v1/trade/history', 'private', 'POST', {'cost': 1})
    private_post_api_spot_v1_trade_fills = privatePostApiSpotV1TradeFills = Entry('api/spot/v1/trade/fills', 'private', 'POST', {'cost': 1})
    private_post_api_spot_v1_plan_placeplan = privatePostApiSpotV1PlanPlacePlan = Entry('api/spot/v1/plan/placePlan', 'private', 'POST', {'cost': 1})
    private_post_api_spot_v1_plan_modifyplan = privatePostApiSpotV1PlanModifyPlan = Entry('api/spot/v1/plan/modifyPlan', 'private', 'POST', {'cost': 1})
    private_post_api_spot_v1_plan_cancelplan = privatePostApiSpotV1PlanCancelPlan = Entry('api/spot/v1/plan/cancelPlan', 'private', 'POST', {'cost': 1})
    private_post_api_spot_v1_plan_currentplan = privatePostApiSpotV1PlanCurrentPlan = Entry('api/spot/v1/plan/currentPlan', 'private', 'POST', {'cost': 1})
    private_post_api_spot_v1_plan_historyplan = privatePostApiSpotV1PlanHistoryPlan = Entry('api/spot/v1/plan/historyPlan', 'private', 'POST', {'cost': 1})
    private_post_api_spot_v1_plan_batchcancelplan = privatePostApiSpotV1PlanBatchCancelPlan = Entry('api/spot/v1/plan/batchCancelPlan', 'private', 'POST', {'cost': 2})
    private_post_api_mix_v1_account_open_count = privatePostApiMixV1AccountOpenCount = Entry('api/mix/v1/account/open-count', 'private', 'POST', {'cost': 1})
    private_post_api_mix_v1_account_setleverage = privatePostApiMixV1AccountSetLeverage = Entry('api/mix/v1/account/setLeverage', 'private', 'POST', {'cost': 4})
    private_post_api_mix_v1_account_setmargin = privatePostApiMixV1AccountSetMargin = Entry('api/mix/v1/account/setMargin', 'private', 'POST', {'cost': 4})
    private_post_api_mix_v1_account_setmarginmode = privatePostApiMixV1AccountSetMarginMode = Entry('api/mix/v1/account/setMarginMode', 'private', 'POST', {'cost': 4})
    private_post_api_mix_v1_account_setpositionmode = privatePostApiMixV1AccountSetPositionMode = Entry('api/mix/v1/account/setPositionMode', 'private', 'POST', {'cost': 4})
    private_post_api_mix_v1_order_placeorder = privatePostApiMixV1OrderPlaceOrder = Entry('api/mix/v1/order/placeOrder', 'private', 'POST', {'cost': 2})
    private_post_api_mix_v1_order_batch_orders = privatePostApiMixV1OrderBatchOrders = Entry('api/mix/v1/order/batch-orders', 'private', 'POST', {'cost': 4, 'step': 10})
    private_post_api_mix_v1_order_cancel_order = privatePostApiMixV1OrderCancelOrder = Entry('api/mix/v1/order/cancel-order', 'private', 'POST', {'cost': 2})
    private_post_api_mix_v1_order_cancel_batch_orders = privatePostApiMixV1OrderCancelBatchOrders = Entry('api/mix/v1/order/cancel-batch-orders', 'private', 'POST', {'cost': 2})
    private_post_api_mix_v1_order_cancel_symbol_orders = privatePostApiMixV1OrderCancelSymbolOrders = Entry('api/mix/v1/order/cancel-symbol-orders', 'private', 'POST', {'cost': 2})
    private_post_api_mix_v1_order_cancel_all_orders = privatePostApiMixV1OrderCancelAllOrders = Entry('api/mix/v1/order/cancel-all-orders', 'private', 'POST', {'cost': 2})
    private_post_api_mix_v1_plan_placeplan = privatePostApiMixV1PlanPlacePlan = Entry('api/mix/v1/plan/placePlan', 'private', 'POST', {'cost': 2})
    private_post_api_mix_v1_plan_modifyplan = privatePostApiMixV1PlanModifyPlan = Entry('api/mix/v1/plan/modifyPlan', 'private', 'POST', {'cost': 2})
    private_post_api_mix_v1_plan_modifyplanpreset = privatePostApiMixV1PlanModifyPlanPreset = Entry('api/mix/v1/plan/modifyPlanPreset', 'private', 'POST', {'cost': 2})
    private_post_api_mix_v1_plan_placetpsl = privatePostApiMixV1PlanPlaceTPSL = Entry('api/mix/v1/plan/placeTPSL', 'private', 'POST', {'cost': 2})
    private_post_api_mix_v1_plan_placetrailstop = privatePostApiMixV1PlanPlaceTrailStop = Entry('api/mix/v1/plan/placeTrailStop', 'private', 'POST', {'cost': 2})
    private_post_api_mix_v1_plan_placepositionstpsl = privatePostApiMixV1PlanPlacePositionsTPSL = Entry('api/mix/v1/plan/placePositionsTPSL', 'private', 'POST', {'cost': 2})
    private_post_api_mix_v1_plan_modifytpslplan = privatePostApiMixV1PlanModifyTPSLPlan = Entry('api/mix/v1/plan/modifyTPSLPlan', 'private', 'POST', {'cost': 2})
    private_post_api_mix_v1_plan_cancelplan = privatePostApiMixV1PlanCancelPlan = Entry('api/mix/v1/plan/cancelPlan', 'private', 'POST', {'cost': 2})
    private_post_api_mix_v1_plan_cancelsymbolplan = privatePostApiMixV1PlanCancelSymbolPlan = Entry('api/mix/v1/plan/cancelSymbolPlan', 'private', 'POST', {'cost': 2})
    private_post_api_mix_v1_plan_cancelallplan = privatePostApiMixV1PlanCancelAllPlan = Entry('api/mix/v1/plan/cancelAllPlan', 'private', 'POST', {'cost': 2})
