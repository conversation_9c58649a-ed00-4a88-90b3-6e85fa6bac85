# -*- coding: utf-8 -*-

# PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
# https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

from ccxt.async_support.base.exchange import Exchange
from ccxt.abstract.deribit import ImplicitAPI
import hashlib
from ccxt.base.types import Account, Any, Balances, Currencies, Currency, DepositAddress, Greeks, Int, Market, Num, Option, OptionChain, Order, OrderBook, OrderSide, OrderType, Position, Str, Strings, Ticker, Tickers, FundingRate, Trade, TradingFees, Transaction, MarketInterface, TransferEntry
from typing import List
from ccxt.base.errors import ExchangeError
from ccxt.base.errors import AuthenticationError
from ccxt.base.errors import PermissionDenied
from ccxt.base.errors import ArgumentsRequired
from ccxt.base.errors import BadRequest
from ccxt.base.errors import InsufficientFunds
from ccxt.base.errors import InvalidAddress
from ccxt.base.errors import InvalidOrder
from ccxt.base.errors import OrderNotFound
from ccxt.base.errors import NotSupported
from ccxt.base.errors import DDoSProtection
from ccxt.base.errors import ExchangeNotAvailable
from ccxt.base.errors import OnMaintenance
from ccxt.base.decimal_to_precision import TICK_SIZE
from ccxt.base.precise import Precise


class deribit(Exchange, ImplicitAPI):

    def describe(self) -> Any:
        return self.deep_extend(super(deribit, self).describe(), {
            'id': 'deribit',
            'name': 'Deribit',
            'countries': ['NL'],  # Netherlands
            'version': 'v2',
            'userAgent': None,
            # 20 requests per second for non-matching-engine endpoints, 1000ms / 20 = 50ms between requests
            # 5 requests per second for matching-engine endpoints, cost = (1000ms / rateLimit) / 5 = 4
            'rateLimit': 50,
            'pro': True,
            'has': {
                'CORS': True,
                'spot': False,
                'margin': False,
                'swap': True,
                'future': True,
                'option': True,
                'cancelAllOrders': True,
                'cancelOrder': True,
                'cancelOrders': False,
                'createDepositAddress': True,
                'createOrder': True,
                'createReduceOnlyOrder': True,
                'createStopLimitOrder': True,
                'createStopMarketOrder': True,
                'createStopOrder': True,
                'createTrailingAmountOrder': True,
                'editOrder': True,
                'fetchAccounts': True,
                'fetchBalance': True,
                'fetchBorrowRateHistories': False,
                'fetchBorrowRateHistory': False,
                'fetchClosedOrders': True,
                'fetchCrossBorrowRate': False,
                'fetchCrossBorrowRates': False,
                'fetchCurrencies': True,
                'fetchDeposit': False,
                'fetchDepositAddress': True,
                'fetchDepositAddresses': False,
                'fetchDepositAddressesByNetwork': False,
                'fetchDeposits': True,
                'fetchDepositWithdrawFees': True,
                'fetchFundingRate': True,
                'fetchFundingRateHistory': True,
                'fetchGreeks': True,
                'fetchIndexOHLCV': False,
                'fetchIsolatedBorrowRate': False,
                'fetchIsolatedBorrowRates': False,
                'fetchLeverageTiers': False,
                'fetchLiquidations': True,
                'fetchMarginMode': False,
                'fetchMarkets': True,
                'fetchMarkOHLCV': False,
                'fetchMyLiquidations': True,
                'fetchMySettlementHistory': False,
                'fetchMyTrades': True,
                'fetchOHLCV': True,
                'fetchOpenOrders': True,
                'fetchOption': True,
                'fetchOptionChain': True,
                'fetchOrder': True,
                'fetchOrderBook': True,
                'fetchOrders': False,
                'fetchOrderTrades': True,
                'fetchPosition': True,
                'fetchPositionMode': False,
                'fetchPositions': True,
                'fetchPremiumIndexOHLCV': False,
                'fetchStatus': True,
                'fetchTicker': True,
                'fetchTickers': True,
                'fetchTime': True,
                'fetchTrades': True,
                'fetchTradingFee': False,
                'fetchTradingFees': True,
                'fetchTransactions': False,
                'fetchTransfer': False,
                'fetchTransfers': True,
                'fetchUnderlyingAssets': False,
                'fetchVolatilityHistory': True,
                'fetchWithdrawal': False,
                'fetchWithdrawals': True,
                'sandbox': True,
                'transfer': True,
                'withdraw': True,
            },
            'timeframes': {
                '1m': '1',
                '3m': '3',
                '5m': '5',
                '10m': '10',
                '15m': '15',
                '30m': '30',
                '1h': '60',
                '2h': '120',
                '3h': '180',
                '6h': '360',
                '12h': '720',
                '1d': '1D',
            },
            'urls': {
                'test': {
                    'rest': 'https://test.deribit.com',
                },
                'logo': 'https://user-images.githubusercontent.com/1294454/41933112-9e2dd65a-798b-11e8-8440-5bab2959fcb8.jpg',
                'api': {
                    'rest': 'https://www.deribit.com',
                },
                'www': 'https://www.deribit.com',
                'doc': [
                    'https://docs.deribit.com/v2',
                    'https://github.com/deribit',
                ],
                'fees': 'https://www.deribit.com/pages/information/fees',
                'referral': {
                    'url': 'https://www.deribit.com/reg-1189.4038',
                    'discount': 0.1,
                },
            },
            'api': {
                'public': {
                    'get': {
                        # Authentication
                        'auth': 1,
                        'exchange_token': 1,
                        'fork_token': 1,
                        # Session management
                        'set_heartbeat': 1,
                        'disable_heartbeat': 1,
                        # Supporting
                        'get_time': 1,
                        'hello': 1,
                        'status': 1,
                        'test': 1,
                        # Subscription management
                        'subscribe': 1,
                        'unsubscribe': 1,
                        'unsubscribe_all': 1,
                        # Account management
                        'get_announcements': 1,
                        # Market data
                        'get_book_summary_by_currency': 1,
                        'get_book_summary_by_instrument': 1,
                        'get_contract_size': 1,
                        'get_currencies': 1,
                        'get_delivery_prices': 1,
                        'get_funding_chart_data': 1,
                        'get_funding_rate_history': 1,
                        'get_funding_rate_value': 1,
                        'get_historical_volatility': 1,
                        'get_index': 1,
                        'get_index_price': 1,
                        'get_index_price_names': 1,
                        'get_instrument': 1,
                        'get_instruments': 1,
                        'get_last_settlements_by_currency': 1,
                        'get_last_settlements_by_instrument': 1,
                        'get_last_trades_by_currency': 1,
                        'get_last_trades_by_currency_and_time': 1,
                        'get_last_trades_by_instrument': 1,
                        'get_last_trades_by_instrument_and_time': 1,
                        'get_mark_price_history': 1,
                        'get_order_book': 1,
                        'get_trade_volumes': 1,
                        'get_tradingview_chart_data': 1,
                        'get_volatility_index_data': 1,
                        'ticker': 1,
                    },
                },
                'private': {
                    'get': {
                        # Authentication
                        'logout': 1,
                        # Session management
                        'enable_cancel_on_disconnect': 1,
                        'disable_cancel_on_disconnect': 1,
                        'get_cancel_on_disconnect': 1,
                        # Subscription management
                        'subscribe': 1,
                        'unsubscribe': 1,
                        'unsubscribe_all': 1,
                        # Account management
                        'change_api_key_name': 1,
                        'change_scope_in_api_key': 1,
                        'change_subaccount_name': 1,
                        'create_api_key': 1,
                        'create_subaccount': 1,
                        'disable_api_key': 1,
                        'disable_tfa_for_subaccount': 1,
                        'enable_affiliate_program': 1,
                        'enable_api_key': 1,
                        'get_access_log': 1,
                        'get_account_summary': 1,
                        'get_account_summaries': 1,
                        'get_affiliate_program_info': 1,
                        'get_email_language': 1,
                        'get_new_announcements': 1,
                        'get_portfolio_margins': 1,
                        'get_position': 1,
                        'get_positions': 1,
                        'get_subaccounts': 1,
                        'get_subaccounts_details': 1,
                        'get_transaction_log': 1,
                        'list_api_keys': 1,
                        'remove_api_key': 1,
                        'remove_subaccount': 1,
                        'reset_api_key': 1,
                        'set_announcement_as_read': 1,
                        'set_api_key_as_default': 1,
                        'set_email_for_subaccount': 1,
                        'set_email_language': 1,
                        'set_password_for_subaccount': 1,
                        'toggle_notifications_from_subaccount': 1,
                        'toggle_subaccount_login': 1,
                        # Block Trade
                        'execute_block_trade': 4,
                        'get_block_trade': 1,
                        'get_last_block_trades_by_currency': 1,
                        'invalidate_block_trade_signature': 1,
                        'verify_block_trade': 4,
                        # Trading
                        'buy': 4,
                        'sell': 4,
                        'edit': 4,
                        'edit_by_label': 4,
                        'cancel': 4,
                        'cancel_all': 4,
                        'cancel_all_by_currency': 4,
                        'cancel_all_by_instrument': 4,
                        'cancel_by_label': 4,
                        'close_position': 4,
                        'get_margins': 1,
                        'get_mmp_config': 1,
                        'get_open_orders_by_currency': 1,
                        'get_open_orders_by_instrument': 1,
                        'get_order_history_by_currency': 1,
                        'get_order_history_by_instrument': 1,
                        'get_order_margin_by_ids': 1,
                        'get_order_state': 1,
                        'get_stop_order_history': 1,  # deprecated
                        'get_trigger_order_history': 1,
                        'get_user_trades_by_currency': 1,
                        'get_user_trades_by_currency_and_time': 1,
                        'get_user_trades_by_instrument': 1,
                        'get_user_trades_by_instrument_and_time': 1,
                        'get_user_trades_by_order': 1,
                        'reset_mmp': 1,
                        'set_mmp_config': 1,
                        'get_settlement_history_by_instrument': 1,
                        'get_settlement_history_by_currency': 1,
                        # Wallet
                        'cancel_transfer_by_id': 1,
                        'cancel_withdrawal': 1,
                        'create_deposit_address': 1,
                        'get_current_deposit_address': 1,
                        'get_deposits': 1,
                        'get_transfers': 1,
                        'get_withdrawals': 1,
                        'submit_transfer_to_subaccount': 1,
                        'submit_transfer_to_user': 1,
                        'withdraw': 1,
                    },
                },
            },
            'features': {
                'default': {
                    'sandbox': True,
                    'createOrder': {
                        'marginMode': False,
                        'triggerPrice': True,  # todo
                        # todo implement
                        'triggerPriceType': {
                            'last': True,
                            'mark': True,
                            'index': True,
                        },
                        'triggerDirection': False,
                        'stopLossPrice': False,  # todo
                        'takeProfitPrice': False,  # todo
                        'attachedStopLossTakeProfit': None,
                        'timeInForce': {
                            'IOC': True,
                            'FOK': True,
                            'PO': True,
                            'GTD': True,
                        },
                        'hedged': False,
                        'selfTradePrevention': False,
                        'trailing': True,  # todo
                        'leverage': False,
                        'marketBuyByCost': True,  # todo
                        'marketBuyRequiresPrice': False,
                        'iceberg': True,  # todo
                    },
                    'createOrders': None,
                    'fetchMyTrades': {
                        'marginMode': False,
                        'limit': 100,  # todo: revise
                        'daysBack': 100000,
                        'untilDays': 100000,
                        'symbolRequired': True,  # todo
                    },
                    'fetchOrder': {
                        'marginMode': False,
                        'trigger': False,
                        'trailing': False,
                        'symbolRequired': True,  # todo
                    },
                    'fetchOpenOrders': {
                        'marginMode': False,
                        'limit': None,
                        'trigger': False,
                        'trailing': False,
                        'symbolRequired': True,  # todo
                    },
                    'fetchOrders': None,
                    'fetchClosedOrders': {
                        'marginMode': False,
                        'limit': 100,
                        'daysBack': 100000,
                        'daysBackCanceled': 1,
                        'untilDays': 100000,
                        'trigger': False,
                        'trailing': False,
                        'symbolRequired': True,  # todo
                    },
                    'fetchOHLCV': {
                        'limit': 1000,  # todo: recheck
                    },
                },
                'spot': {
                    'extends': 'default',
                },
                'swap': {
                    'linear': {
                        'extends': 'default',
                    },
                    'inverse': {
                        'extends': 'default',
                    },
                },
                'future': {
                    'linear': {
                        'extends': 'default',
                    },
                    'inverse': {
                        'extends': 'default',
                    },
                },
            },
            'exceptions': {
                # 0 or absent Success, No error.
                '9999': PermissionDenied,  # 'api_not_enabled' User didn't enable API for the Account.
                '10000': AuthenticationError,  # 'authorization_required' Authorization issue, invalid or absent signature etc.
                '10001': ExchangeError,  # 'error' Some general failure, no public information available.
                '10002': InvalidOrder,  # 'qty_too_low' Order quantity is too low.
                '10003': InvalidOrder,  # 'order_overlap' Rejection, order overlap is found and self-trading is not enabled.
                '10004': OrderNotFound,  # 'order_not_found' Attempt to operate with order that can't be found by specified id.
                '10005': InvalidOrder,  # 'price_too_low <Limit>' Price is too low, <Limit> defines current limit for the operation.
                '10006': InvalidOrder,  # 'price_too_low4idx <Limit>' Price is too low for current index, <Limit> defines current bottom limit for the operation.
                '10007': InvalidOrder,  # 'price_too_high <Limit>' Price is too high, <Limit> defines current up limit for the operation.
                '10008': InvalidOrder,  # 'price_too_high4idx <Limit>' Price is too high for current index, <Limit> defines current up limit for the operation.
                '10009': InsufficientFunds,  # 'not_enough_funds' Account has not enough funds for the operation.
                '10010': OrderNotFound,  # 'already_closed' Attempt of doing something with closed order.
                '10011': InvalidOrder,  # 'price_not_allowed' This price is not allowed for some reason.
                '10012': InvalidOrder,  # 'book_closed' Operation for instrument which order book had been closed.
                '10013': PermissionDenied,  # 'pme_max_total_open_orders <Limit>' Total limit of open orders has been exceeded, it is applicable for PME users.
                '10014': PermissionDenied,  # 'pme_max_future_open_orders <Limit>' Limit of count of futures' open orders has been exceeded, it is applicable for PME users.
                '10015': PermissionDenied,  # 'pme_max_option_open_orders <Limit>' Limit of count of options' open orders has been exceeded, it is applicable for PME users.
                '10016': PermissionDenied,  # 'pme_max_future_open_orders_size <Limit>' Limit of size for futures has been exceeded, it is applicable for PME users.
                '10017': PermissionDenied,  # 'pme_max_option_open_orders_size <Limit>' Limit of size for options has been exceeded, it is applicable for PME users.
                '10018': PermissionDenied,  # 'non_pme_max_future_position_size <Limit>' Limit of size for futures has been exceeded, it is applicable for non-PME users.
                '10019': PermissionDenied,  # 'locked_by_admin' Trading is temporary locked by admin.
                '10020': ExchangeError,  # 'invalid_or_unsupported_instrument' Instrument name is not valid.
                '10021': InvalidOrder,  # 'invalid_amount' Amount is not valid.
                '10022': InvalidOrder,  # 'invalid_quantity' quantity was not recognized valid number(for API v1).
                '10023': InvalidOrder,  # 'invalid_price' price was not recognized valid number.
                '10024': InvalidOrder,  # 'invalid_max_show' max_show parameter was not recognized valid number.
                '10025': InvalidOrder,  # 'invalid_order_id' Order id is missing or its format was not recognized.
                '10026': InvalidOrder,  # 'price_precision_exceeded' Extra precision of the price is not supported.
                '10027': InvalidOrder,  # 'non_integer_contract_amount' Futures contract amount was not recognized.
                '10028': DDoSProtection,  # 'too_many_requests' Allowed request rate has been exceeded.
                '10029': OrderNotFound,  # 'not_owner_of_order' Attempt to operate with not own order.
                '10030': ExchangeError,  # 'must_be_websocket_request' REST request where Websocket is expected.
                '10031': ExchangeError,  # 'invalid_args_for_instrument' Some of arguments are not recognized.
                '10032': InvalidOrder,  # 'whole_cost_too_low' Total cost is too low.
                '10033': NotSupported,  # 'not_implemented' Method is not implemented yet.
                '10034': InvalidOrder,  # 'stop_price_too_high' Stop price is too high.
                '10035': InvalidOrder,  # 'stop_price_too_low' Stop price is too low.
                '10036': InvalidOrder,  # 'invalid_max_show_amount' Max Show Amount is not valid.
                '10040': ExchangeNotAvailable,  # 'retry' Request can't be processed right now and should be retried.
                '10041': OnMaintenance,  # 'settlement_in_progress' Settlement is in progress. Every day at settlement time for several seconds, the system calculates user profits and updates balances. That time trading is paused for several seconds till the calculation is completed.
                '10043': InvalidOrder,  # 'price_wrong_tick' Price has to be rounded to a certain tick size.
                '10044': InvalidOrder,  # 'stop_price_wrong_tick' Stop Price has to be rounded to a certain tick size.
                '10045': InvalidOrder,  # 'can_not_cancel_liquidation_order' Liquidation order can't be canceled.
                '10046': InvalidOrder,  # 'can_not_edit_liquidation_order' Liquidation order can't be edited.
                '10047': DDoSProtection,  # 'matching_engine_queue_full' Reached limit of pending Matching Engine requests for user.
                '10048': ExchangeError,  # 'not_on_self_server' The requested operation is not available on self server.
                '11008': InvalidOrder,  # 'already_filled' This request is not allowed in regards to the filled order.
                '11029': BadRequest,  # 'invalid_arguments' Some invalid input has been detected.
                '11030': ExchangeError,  # 'other_reject <Reason>' Some rejects which are not considered often, more info may be specified in <Reason>.
                '11031': ExchangeError,  # 'other_error <Error>' Some errors which are not considered often, more info may be specified in <Error>.
                '11035': DDoSProtection,  # 'no_more_stops <Limit>' Allowed amount of stop orders has been exceeded.
                '11036': InvalidOrder,  # 'invalid_stoppx_for_index_or_last' Invalid StopPx(too high or too low) current index or market.
                '11037': BadRequest,  # 'outdated_instrument_for_IV_order' Instrument already not available for trading.
                '11038': InvalidOrder,  # 'no_adv_for_futures' Advanced orders are not available for futures.
                '11039': InvalidOrder,  # 'no_adv_postonly' Advanced post-only orders are not supported yet.
                '11041': InvalidOrder,  # 'not_adv_order' Advanced order properties can't be set if the order is not advanced.
                '11042': PermissionDenied,  # 'permission_denied' Permission for the operation has been denied.
                '11043': BadRequest,  # 'bad_argument' Bad argument has been passed.
                '11044': InvalidOrder,  # 'not_open_order' Attempt to do open order operations with the not open order.
                '11045': BadRequest,  # 'invalid_event' Event name has not been recognized.
                '11046': BadRequest,  # 'outdated_instrument' At several minutes to instrument expiration, corresponding advanced implied volatility orders are not allowed.
                '11047': BadRequest,  # 'unsupported_arg_combination' The specified combination of arguments is not supported.
                '11048': ExchangeError,  # 'wrong_max_show_for_option' Wrong Max Show for options.
                '11049': BadRequest,  # 'bad_arguments' Several bad arguments have been passed.
                '11050': BadRequest,  # 'bad_request' Request has not been parsed properly.
                '11051': OnMaintenance,  # 'system_maintenance' System is under maintenance.
                '11052': ExchangeError,  # 'subscribe_error_unsubscribed' Subscription error. However, subscription may fail without self error, please check list of subscribed channels returned, channels can be not subscribed due to wrong input or lack of permissions.
                '11053': ExchangeError,  # 'transfer_not_found' Specified transfer is not found.
                '11090': InvalidAddress,  # 'invalid_addr' Invalid address.
                '11091': InvalidAddress,  # 'invalid_transfer_address' Invalid addres for the transfer.
                '11092': InvalidAddress,  # 'address_already_exist' The address already exists.
                '11093': DDoSProtection,  # 'max_addr_count_exceeded' Limit of allowed addresses has been reached.
                '11094': ExchangeError,  # 'internal_server_error' Some unhandled error on server. Please report to admin. The details of the request will help to locate the problem.
                '11095': ExchangeError,  # 'disabled_deposit_address_creation' Deposit address creation has been disabled by admin.
                '11096': ExchangeError,  # 'address_belongs_to_user' Withdrawal instead of transfer.
                '12000': AuthenticationError,  # 'bad_tfa' Wrong TFA code
                '12001': DDoSProtection,  # 'too_many_subaccounts' Limit of subbacounts is reached.
                '12002': ExchangeError,  # 'wrong_subaccount_name' The input is not allowed of subaccount.
                '12998': AuthenticationError,  # 'tfa_over_limit' The number of failed TFA attempts is limited.
                '12003': AuthenticationError,  # 'login_over_limit' The number of failed login attempts is limited.
                '12004': AuthenticationError,  # 'registration_over_limit' The number of registration requests is limited.
                '12005': AuthenticationError,  # 'country_is_banned' The country is banned(possibly via IP check).
                '12100': ExchangeError,  # 'transfer_not_allowed' Transfer is not allowed. Possible wrong direction or other mistake.
                '12999': AuthenticationError,  # 'tfa_used' TFA code is correct but it is already used. Please, use next code.
                '13000': AuthenticationError,  # 'invalid_login' Login name is invalid(not allowed or it contains wrong characters).
                '13001': AuthenticationError,  # 'account_not_activated' Account must be activated.
                '13002': PermissionDenied,  # 'account_blocked' Account is blocked by admin.
                '13003': AuthenticationError,  # 'tfa_required' This action requires TFA authentication.
                '13004': AuthenticationError,  # 'invalid_credentials' Invalid credentials has been used.
                '13005': AuthenticationError,  # 'pwd_match_error' Password confirmation error.
                '13006': AuthenticationError,  # 'security_error' Invalid Security Code.
                '13007': AuthenticationError,  # 'user_not_found' User's security code has been changed or wrong.
                '13008': ExchangeError,  # 'request_failed' Request failed because of invalid input or internal failure.
                '13009': AuthenticationError,  # 'unauthorized' Wrong or expired authorization token or bad signature. For example, please check scope of the token, 'connection' scope can't be reused for other connections.
                '13010': BadRequest,  # 'value_required' Invalid input, missing value.
                '13011': BadRequest,  # 'value_too_short' Input is too short.
                '13012': PermissionDenied,  # 'unavailable_in_subaccount' Subaccount restrictions.
                '13013': BadRequest,  # 'invalid_phone_number' Unsupported or invalid phone number.
                '13014': BadRequest,  # 'cannot_send_sms' SMS sending failed -- phone number is wrong.
                '13015': BadRequest,  # 'invalid_sms_code' Invalid SMS code.
                '13016': BadRequest,  # 'invalid_input' Invalid input.
                '13017': ExchangeError,  # 'subscription_failed' Subscription hailed, invalid subscription parameters.
                '13018': ExchangeError,  # 'invalid_content_type' Invalid content type of the request.
                '13019': ExchangeError,  # 'orderbook_closed' Closed, expired order book.
                '13020': ExchangeError,  # 'not_found' Instrument is not found, invalid instrument name.
                '13021': PermissionDenied,  # 'forbidden' Not enough permissions to execute the request, forbidden.
                '13025': ExchangeError,  # 'method_switched_off_by_admin' API method temporarily switched off by administrator.
                '-32602': BadRequest,  # 'Invalid params' see JSON-RPC spec.
                '-32601': BadRequest,  # 'Method not found' see JSON-RPC spec.
                '-32700': BadRequest,  # 'Parse error' see JSON-RPC spec.
                '-32000': BadRequest,  # 'Missing params' see JSON-RPC spec.
                '11054': InvalidOrder,  # 'post_only_reject' post order would be filled immediately
            },
            'precisionMode': TICK_SIZE,
            'options': {
                'code': 'BTC',
                'fetchBalance': {
                    'code': 'BTC',
                },
                'transfer': {
                    'method': 'privateGetSubmitTransferToSubaccount',  # or 'privateGetSubmitTransferToUser'
                },
            },
        })

    def create_expired_option_market(self, symbol: str):
        # support expired option contracts
        quote = 'USD'
        settle = None
        optionParts = symbol.split('-')
        symbolBase = symbol.split('/')
        base = None
        expiry = None
        if symbol.find('/') > -1:
            base = self.safe_string(symbolBase, 0)
            expiry = self.safe_string(optionParts, 1)
            if symbol.find('USDC') > -1:
                base = base + '_USDC'
        else:
            base = self.safe_string(optionParts, 0)
            expiry = self.convert_market_id_expire_date(self.safe_string(optionParts, 1))
        if symbol.find('USDC') > -1:
            quote = 'USDC'
            settle = 'USDC'
        else:
            settle = base
        splitBase = base
        if base.find('_') > -1:
            splitSymbol = base.split('_')
            splitBase = self.safe_string(splitSymbol, 0)
        strike = self.safe_string(optionParts, 2)
        optionType = self.safe_string(optionParts, 3)
        datetime = self.convert_expire_date(expiry)
        timestamp = self.parse8601(datetime)
        return {
            'id': base + '-' + self.convert_expire_date_to_market_id_date(expiry) + '-' + strike + '-' + optionType,
            'symbol': splitBase + '/' + quote + ':' + settle + '-' + expiry + '-' + strike + '-' + optionType,
            'base': base,
            'quote': quote,
            'settle': settle,
            'baseId': base,
            'quoteId': quote,
            'settleId': settle,
            'active': False,
            'type': 'option',
            'linear': None,
            'inverse': None,
            'spot': False,
            'swap': False,
            'future': False,
            'option': True,
            'margin': False,
            'contract': True,
            'contractSize': None,
            'expiry': timestamp,
            'expiryDatetime': datetime,
            'optionType': 'call' if (optionType == 'C') else 'put',
            'strike': self.parse_number(strike),
            'precision': {
                'amount': None,
                'price': None,
            },
            'limits': {
                'amount': {
                    'min': None,
                    'max': None,
                },
                'price': {
                    'min': None,
                    'max': None,
                },
                'cost': {
                    'min': None,
                    'max': None,
                },
            },
            'info': None,
        }

    def safe_market(self, marketId: Str = None, market: Market = None, delimiter: Str = None, marketType: Str = None) -> MarketInterface:
        isOption = (marketId is not None) and ((marketId.endswith('-C')) or (marketId.endswith('-P')))
        if isOption and not (marketId in self.markets_by_id):
            # handle expired option contracts
            return self.create_expired_option_market(marketId)
        return super(deribit, self).safe_market(marketId, market, delimiter, marketType)

    async def fetch_time(self, params={}) -> Int:
        """
        fetches the current integer timestamp in milliseconds from the exchange server

        https://docs.deribit.com/#public-get_time

        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns int: the current integer timestamp in milliseconds from the exchange server
        """
        response = await self.publicGetGetTime(params)
        #
        #     {
        #         "jsonrpc": "2.0",
        #         "result": 1583922446019,
        #         "usIn": 1583922446019955,
        #         "usOut": 1583922446019956,
        #         "usDiff": 1,
        #         "testnet": False
        #     }
        #
        return self.safe_integer(response, 'result')

    async def fetch_currencies(self, params={}) -> Currencies:
        """
        fetches all available currencies on an exchange

        https://docs.deribit.com/#public-get_currencies

        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: an associative dictionary of currencies
        """
        response = await self.publicGetGetCurrencies(params)
        #
        #    {
        #      "jsonrpc": "2.0",
        #      "result": [
        #        {
        #          "withdrawal_priorities": [],
        #          "withdrawal_fee": 0.01457324,
        #          "min_withdrawal_fee": 0.000001,
        #          "min_confirmations": 1,
        #          "fee_precision": 8,
        #          "currency_long": "Solana",
        #          "currency": "SOL",
        #          "coin_type": "SOL"
        #        },
        #        ...
        #      ],
        #      "usIn": 1688652701456124,
        #      "usOut": 1688652701456390,
        #      "usDiff": 266,
        #      "testnet": True
        #    }
        #
        data = self.safe_list(response, 'result', [])
        result: dict = {}
        for i in range(0, len(data)):
            currency = data[i]
            currencyId = self.safe_string(currency, 'currency')
            code = self.safe_currency_code(currencyId)
            result[code] = self.safe_currency_structure({
                'info': currency,
                'code': code,
                'id': currencyId,
                'name': self.safe_string(currency, 'currency_long'),
                'active': None,
                'deposit': None,
                'withdraw': None,
                'type': 'crypto',
                'fee': self.safe_number(currency, 'withdrawal_fee'),
                'precision': self.parse_number(self.parse_precision(self.safe_string(currency, 'fee_precision'))),
                'limits': {
                    'amount': {
                        'min': None,
                        'max': None,
                    },
                    'withdraw': {
                        'min': None,
                        'max': None,
                    },
                    'deposit': {
                        'min': None,
                        'max': None,
                    },
                },
                'networks': None,
            })
        return result

    def code_from_options(self, methodName, params={}):
        defaultCode = self.safe_value(self.options, 'code', 'BTC')
        options = self.safe_value(self.options, methodName, {})
        code = self.safe_value(options, 'code', defaultCode)
        return self.safe_value(params, 'code', code)

    async def fetch_status(self, params={}):
        """
        the latest known information on the availability of the exchange API

        https://docs.deribit.com/#public-status

        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `status structure <https://docs.ccxt.com/#/?id=exchange-status-structure>`
        """
        response = await self.publicGetStatus(params)
        #
        #     {
        #         "jsonrpc": "2.0",
        #         "result": {
        #             "locked": "false"  # True, partial, False
        #         },
        #         "usIn": 1650641690226788,
        #         "usOut": 1650641690226836,
        #         "usDiff": 48,
        #         "testnet": False
        #     }
        #
        result = self.safe_value(response, 'result')
        locked = self.safe_string(result, 'locked')
        updateTime = self.safe_integer_product(response, 'usIn', 0.001, self.milliseconds())
        return {
            'status': 'ok' if (locked == 'false') else 'maintenance',
            'updated': updateTime,
            'eta': None,
            'url': None,
            'info': response,
        }

    async def fetch_accounts(self, params={}) -> List[Account]:
        """
        fetch all the accounts associated with a profile

        https://docs.deribit.com/#private-get_subaccounts

        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a dictionary of `account structures <https://docs.ccxt.com/#/?id=account-structure>` indexed by the account type
        """
        await self.load_markets()
        response = await self.privateGetGetSubaccounts(params)
        #
        #     {
        #         "jsonrpc": "2.0",
        #         "result": [{
        #                 "username": "someusername",
        #                 "type": "main",
        #                 "system_name": "someusername",
        #                 "security_keys_enabled": False,
        #                 "security_keys_assignments": [],
        #                 "receive_notifications": False,
        #                 "login_enabled": True,
        #                 "is_password": True,
        #                 "id": "238216",
        #                 "email": "<EMAIL>"
        #             },
        #             {
        #                 "username": "someusername_1",
        #                 "type": "subaccount",
        #                 "system_name": "someusername_1",
        #                 "security_keys_enabled": False,
        #                 "security_keys_assignments": [],
        #                 "receive_notifications": False,
        #                 "login_enabled": False,
        #                 "is_password": False,
        #                 "id": "245499",
        #                 "email": "<EMAIL>"
        #             }
        #         ],
        #         "usIn": "****************",
        #         "usOut": "****************",
        #         "usDiff": "371",
        #         "testnet": False
        #     }
        #
        result = self.safe_value(response, 'result', [])
        return self.parse_accounts(result)

    def parse_account(self, account):
        #
        #      {
        #          "username": "someusername_1",
        #          "type": "subaccount",
        #          "system_name": "someusername_1",
        #          "security_keys_enabled": False,
        #          "security_keys_assignments": [],
        #          "receive_notifications": False,
        #          "login_enabled": False,
        #          "is_password": False,
        #          "id": "245499",
        #          "email": "<EMAIL>"
        #      }
        #
        return {
            'info': account,
            'id': self.safe_string(account, 'id'),
            'type': self.safe_string(account, 'type'),
            'code': None,
        }

    async def fetch_markets(self, params={}) -> List[Market]:
        """
        retrieves data on all markets for deribit

        https://docs.deribit.com/#public-get_currencies
        https://docs.deribit.com/#public-get_instruments

        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: an array of objects representing market data
        """
        instrumentsResponses = []
        result = []
        parsedMarkets: dict = {}
        fetchAllMarkets = None
        fetchAllMarkets, params = self.handle_option_and_params(params, 'fetchMarkets', 'fetchAllMarkets', True)
        if fetchAllMarkets:
            instrumentsResponse = await self.publicGetGetInstruments(params)
            instrumentsResponses.append(instrumentsResponse)
        else:
            currenciesResponse = await self.publicGetGetCurrencies(params)
            #
            #     {
            #         "jsonrpc": "2.0",
            #         "result": [
            #             {
            #                 "withdrawal_priorities": [
            #                     {value: 0.15, name: "very_low"},
            #                     {value: 1.5, name: "very_high"},
            #                 ],
            #                 "withdrawal_fee": 0.0005,
            #                 "min_withdrawal_fee": 0.0005,
            #                 "min_confirmations": 1,
            #                 "fee_precision": 4,
            #                 "currency_long": "Bitcoin",
            #                 "currency": "BTC",
            #                 "coin_type": "BITCOIN"
            #             }
            #         ],
            #         "usIn": 1583761588590479,
            #         "usOut": 1583761588590544,
            #         "usDiff": 65,
            #         "testnet": False
            #     }
            #
            currenciesResult = self.safe_value(currenciesResponse, 'result', [])
            for i in range(0, len(currenciesResult)):
                currencyId = self.safe_string(currenciesResult[i], 'currency')
                request: dict = {
                    'currency': currencyId,
                }
                instrumentsResponse = await self.publicGetGetInstruments(self.extend(request, params))
                #
                #     {
                #         "jsonrpc":"2.0",
                #         "result":[
                #             {
                #                 "tick_size":0.0005,
                #                 "taker_commission":0.0003,
                #                 "strike":52000.0,
                #                 "settlement_period":"month",
                #                 "settlement_currency":"BTC",
                #                 "quote_currency":"BTC",
                #                 "option_type":"put",  # put, call
                #                 "min_trade_amount":0.1,
                #                 "maker_commission":0.0003,
                #                 "kind":"option",
                #                 "is_active":true,
                #                 "instrument_name":"BTC-24JUN22-52000-P",
                #                 "expiration_timestamp":1656057600000,
                #                 "creation_timestamp":1648199543000,
                #                 "counter_currency":"USD",
                #                 "contract_size":1.0,
                #                 "block_trade_commission":0.0003,
                #                 "base_currency":"BTC"
                #             },
                #             {
                #                 "tick_size":0.5,
                #                 "taker_commission":0.0005,
                #                 "settlement_period":"month",  # month, week
                #                 "settlement_currency":"BTC",
                #                 "quote_currency":"USD",
                #                 "min_trade_amount":10.0,
                #                 "max_liquidation_commission":0.0075,
                #                 "max_leverage":50,
                #                 "maker_commission":0.0,
                #                 "kind":"future",
                #                 "is_active":true,
                #                 "instrument_name":"BTC-27MAY22",
                #                 "future_type":"reversed",
                #                 "expiration_timestamp":1653638400000,
                #                 "creation_timestamp":1648195209000,
                #                 "counter_currency":"USD",
                #                 "contract_size":10.0,
                #                 "block_trade_commission":0.0001,
                #                 "base_currency":"BTC"
                #             },
                #             {
                #                 "tick_size":0.5,
                #                 "taker_commission":0.0005,
                #                 "settlement_period":"perpetual",
                #                 "settlement_currency":"BTC",
                #                 "quote_currency":"USD",
                #                 "min_trade_amount":10.0,
                #                 "max_liquidation_commission":0.0075,
                #                 "max_leverage":50,
                #                 "maker_commission":0.0,
                #                 "kind":"future",
                #                 "is_active":true,
                #                 "instrument_name":"BTC-PERPETUAL",
                #                 "future_type":"reversed",
                #                 "expiration_timestamp":32503708800000,
                #                 "creation_timestamp":1534242287000,
                #                 "counter_currency":"USD",
                #                 "contract_size":10.0,
                #                 "block_trade_commission":0.0001,
                #                 "base_currency":"BTC"
                #             },
                #         ],
                #         "usIn":1648691472831791,
                #         "usOut":1648691472831896,
                #         "usDiff":105,
                #         "testnet":false
                #     }
                #
                instrumentsResponses.append(instrumentsResponse)
        for i in range(0, len(instrumentsResponses)):
            instrumentsResult = self.safe_value(instrumentsResponses[i], 'result', [])
            for k in range(0, len(instrumentsResult)):
                market = instrumentsResult[k]
                kind = self.safe_string(market, 'kind')
                isSpot = (kind == 'spot')
                id = self.safe_string(market, 'instrument_name')
                baseId = self.safe_string(market, 'base_currency')
                quoteId = self.safe_string(market, 'counter_currency')
                settleId = self.safe_string(market, 'settlement_currency')
                base = self.safe_currency_code(baseId)
                quote = self.safe_currency_code(quoteId)
                settle = self.safe_currency_code(settleId)
                settlementPeriod = self.safe_value(market, 'settlement_period')
                swap = (settlementPeriod == 'perpetual')
                future = not swap and (kind.find('future') >= 0)
                option = (kind.find('option') >= 0)
                isComboMarket = kind.find('combo') >= 0
                expiry = self.safe_integer(market, 'expiration_timestamp')
                strike = None
                optionType = None
                symbol = id
                type = 'swap'
                if future:
                    type = 'future'
                elif option:
                    type = 'option'
                elif isSpot:
                    type = 'spot'
                inverse = None
                linear = None
                if isSpot:
                    symbol = base + '/' + quote
                elif not isComboMarket:
                    symbol = base + '/' + quote + ':' + settle
                    if option or future:
                        symbol = symbol + '-' + self.yymmdd(expiry, '')
                        if option:
                            strike = self.safe_number(market, 'strike')
                            optionType = self.safe_string(market, 'option_type')
                            letter = 'C' if (optionType == 'call') else 'P'
                            symbol = symbol + '-' + self.number_to_string(strike) + '-' + letter
                    inverse = (quote != settle)
                    linear = (settle == quote)
                parsedMarketValue = self.safe_value(parsedMarkets, symbol)
                if parsedMarketValue:
                    continue
                parsedMarkets[symbol] = True
                minTradeAmount = self.safe_number(market, 'min_trade_amount')
                tickSize = self.safe_number(market, 'tick_size')
                result.append({
                    'id': id,
                    'symbol': symbol,
                    'base': base,
                    'quote': quote,
                    'settle': settle,
                    'baseId': baseId,
                    'quoteId': quoteId,
                    'settleId': settleId,
                    'type': type,
                    'spot': isSpot,
                    'margin': False,
                    'swap': swap,
                    'future': future,
                    'option': option,
                    'active': self.safe_value(market, 'is_active'),
                    'contract': not isSpot,
                    'linear': linear,
                    'inverse': inverse,
                    'taker': self.safe_number(market, 'taker_commission'),
                    'maker': self.safe_number(market, 'maker_commission'),
                    'contractSize': self.safe_number(market, 'contract_size'),
                    'expiry': expiry,
                    'expiryDatetime': self.iso8601(expiry),
                    'strike': strike,
                    'optionType': optionType,
                    'precision': {
                        'amount': minTradeAmount,
                        'price': tickSize,
                    },
                    'limits': {
                        'leverage': {
                            'min': None,
                            'max': None,
                        },
                        'amount': {
                            'min': minTradeAmount,
                            'max': None,
                        },
                        'price': {
                            'min': tickSize,
                            'max': None,
                        },
                        'cost': {
                            'min': None,
                            'max': None,
                        },
                    },
                    'created': self.safe_integer(market, 'creation_timestamp'),
                    'info': market,
                })
        return result

    def parse_balance(self, balance) -> Balances:
        result: dict = {
            'info': balance,
        }
        summaries = []
        if 'summaries' in balance:
            summaries = self.safe_list(balance, 'summaries')
        else:
            summaries = [balance]
        for i in range(0, len(summaries)):
            data = summaries[i]
            currencyId = self.safe_string(data, 'currency')
            currencyCode = self.safe_currency_code(currencyId)
            account = self.account()
            account['free'] = self.safe_string(data, 'available_funds')
            account['used'] = self.safe_string(data, 'maintenance_margin')
            account['total'] = self.safe_string(data, 'equity')
            result[currencyCode] = account
        return self.safe_balance(result)

    async def fetch_balance(self, params={}) -> Balances:
        """
        query for balance and get the amount of funds available for trading or funds locked in orders

        https://docs.deribit.com/#private-get_account_summary
        https://docs.deribit.com/#private-get_account_summaries

        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param str [params.code]: unified currency code of the currency for the balance, if defined 'privateGetGetAccountSummary' will be used, otherwise 'privateGetGetAccountSummaries' will be used
        :returns dict: a `balance structure <https://docs.ccxt.com/#/?id=balance-structure>`
        """
        await self.load_markets()
        code = self.safe_string(params, 'code')
        params = self.omit(params, 'code')
        request: dict = {
        }
        if code is not None:
            request['currency'] = self.currency_id(code)
        response = None
        if code is None:
            response = await self.privateGetGetAccountSummaries(params)
        else:
            response = await self.privateGetGetAccountSummary(self.extend(request, params))
        #
        #     {
        #         "jsonrpc": "2.0",
        #         "result": {
        #             "total_pl": 0,
        #             "session_upl": 0,
        #             "session_rpl": 0,
        #             "session_funding": 0,
        #             "portfolio_margining_enabled": False,
        #             "options_vega": 0,
        #             "options_theta": 0,
        #             "options_session_upl": 0,
        #             "options_session_rpl": 0,
        #             "options_pl": 0,
        #             "options_gamma": 0,
        #             "options_delta": 0,
        #             "margin_balance": 0.********,
        #             "maintenance_margin": 0,
        #             "limits": {
        #                 "non_matching_engine_burst": 300,
        #                 "non_matching_engine": 200,
        #                 "matching_engine_burst": 20,
        #                 "matching_engine": 2
        #             },
        #             "initial_margin": 0,
        #             "futures_session_upl": 0,
        #             "futures_session_rpl": 0,
        #             "futures_pl": 0,
        #             "equity": 0.********,
        #             "deposit_address": "**********************************",
        #             "delta_total": 0,
        #             "currency": "BTC",
        #             "balance": 0.********,
        #             "available_withdrawal_funds": 0.********,
        #             "available_funds": 0.********
        #         },
        #         "usIn": 1583775838115975,
        #         "usOut": 1583775838116520,
        #         "usDiff": 545,
        #         "testnet": False
        #     }
        #
        result = self.safe_dict(response, 'result', {})
        return self.parse_balance(result)

    async def create_deposit_address(self, code: str, params={}) -> DepositAddress:
        """
        create a currency deposit address

        https://docs.deribit.com/#private-create_deposit_address

        :param str code: unified currency code of the currency for the deposit address
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: an `address structure <https://docs.ccxt.com/#/?id=address-structure>`
        """
        await self.load_markets()
        currency = self.currency(code)
        request: dict = {
            'currency': currency['id'],
        }
        response = await self.privateGetCreateDepositAddress(self.extend(request, params))
        #
        #     {
        #         "jsonrpc": "2.0",
        #         "id": 7538,
        #         "result": {
        #             "address": "2N8udZGBc1hLRCFsU9kGwMPpmYUwMFTuCwB",
        #             "creation_timestamp": 1550575165170,
        #             "currency": "BTC",
        #             "type": "deposit"
        #         }
        #     }
        #
        result = self.safe_value(response, 'result', {})
        address = self.safe_string(result, 'address')
        self.check_address(address)
        return {
            'currency': code,
            'address': address,
            'tag': None,
            'network': None,
            'info': response,
        }

    async def fetch_deposit_address(self, code: str, params={}) -> DepositAddress:
        """
        fetch the deposit address for a currency associated with self account

        https://docs.deribit.com/#private-get_current_deposit_address

        :param str code: unified currency code
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: an `address structure <https://docs.ccxt.com/#/?id=address-structure>`
        """
        await self.load_markets()
        currency = self.currency(code)
        request: dict = {
            'currency': currency['id'],
        }
        response = await self.privateGetGetCurrentDepositAddress(self.extend(request, params))
        #
        #     {
        #         "jsonrpc": "2.0",
        #         "result": {
        #             "type": "deposit",
        #             "status": "ready",
        #             "requires_confirmation": True,
        #             "currency": "BTC",
        #             "creation_timestamp": *************,
        #             "address": "**********************************"
        #         },
        #         "usIn": 1583785137274288,
        #         "usOut": 1583785137274454,
        #         "usDiff": 166,
        #         "testnet": False
        #     }
        #
        result = self.safe_value(response, 'result', {})
        address = self.safe_string(result, 'address')
        self.check_address(address)
        return {
            'info': response,
            'currency': code,
            'network': None,
            'address': address,
            'tag': None,
        }

    def parse_ticker(self, ticker: dict, market: Market = None) -> Ticker:
        #
        # fetchTicker /public/ticker
        #
        #     {
        #         "timestamp": 1583778859480,
        #         "stats": {volume: 60627.57263769, low: 7631.5, high: 8311.5},
        #         "state": "open",
        #         "settlement_price": 7903.21,
        #         "open_interest": 111543850,
        #         "min_price": 7634,
        #         "max_price": 7866.51,
        #         "mark_price": 7750.02,
        #         "last_price": 7750.5,
        #         "instrument_name": "BTC-PERPETUAL",
        #         "index_price": 7748.01,
        #         "funding_8h": 0.0000026,
        #         "current_funding": 0,
        #         "best_bid_price": 7750,
        #         "best_bid_amount": 19470,
        #         "best_ask_price": 7750.5,
        #         "best_ask_amount": 343280
        #     }
        #
        # fetchTicker /public/get_book_summary_by_instrument
        # fetchTickers /public/get_book_summary_by_currency
        #
        #     {
        #         "volume": 124.1,
        #         "underlying_price": 7856.445926872601,
        #         "underlying_index": "SYN.BTC-10MAR20",
        #         "quote_currency": "USD",
        #         "open_interest": 121.8,
        #         "mid_price": 0.01975,
        #         "mark_price": 0.01984559,
        #         "low": 0.0095,
        #         "last": 0.0205,
        #         "interest_rate": 0,
        #         "instrument_name": "BTC-10MAR20-7750-C",
        #         "high": 0.0295,
        #         "estimated_delivery_price": 7856.29,
        #         "creation_timestamp": 1583783678366,
        #         "bid_price": 0.0185,
        #         "base_currency": "BTC",
        #         "ask_price": 0.021
        #     },
        #
        timestamp = self.safe_integer_2(ticker, 'timestamp', 'creation_timestamp')
        marketId = self.safe_string(ticker, 'instrument_name')
        symbol = self.safe_symbol(marketId, market)
        last = self.safe_string_2(ticker, 'last_price', 'last')
        stats = self.safe_value(ticker, 'stats', ticker)
        return self.safe_ticker({
            'symbol': symbol,
            'timestamp': timestamp,
            'datetime': self.iso8601(timestamp),
            'high': self.safe_string_2(stats, 'high', 'max_price'),
            'low': self.safe_string_2(stats, 'low', 'min_price'),
            'bid': self.safe_string_2(ticker, 'best_bid_price', 'bid_price'),
            'bidVolume': self.safe_string(ticker, 'best_bid_amount'),
            'ask': self.safe_string_2(ticker, 'best_ask_price', 'ask_price'),
            'askVolume': self.safe_string(ticker, 'best_ask_amount'),
            'vwap': None,
            'open': None,
            'close': last,
            'last': last,
            'previousClose': None,
            'change': None,
            'percentage': None,
            'average': None,
            'baseVolume': None,
            'quoteVolume': self.safe_string(stats, 'volume'),
            'markPrice': self.safe_string(ticker, 'mark_price'),
            'indexPrice': self.safe_string(ticker, 'index_price'),
            'info': ticker,
        }, market)

    async def fetch_ticker(self, symbol: str, params={}) -> Ticker:
        """
        fetches a price ticker, a statistical calculation with the information calculated over the past 24 hours for a specific market

        https://docs.deribit.com/#public-ticker

        :param str symbol: unified symbol of the market to fetch the ticker for
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `ticker structure <https://docs.ccxt.com/#/?id=ticker-structure>`
        """
        await self.load_markets()
        market = self.market(symbol)
        request: dict = {
            'instrument_name': market['id'],
        }
        response = await self.publicGetTicker(self.extend(request, params))
        #
        #     {
        #         "jsonrpc": "2.0",
        #         "result": {
        #             "timestamp": 1583778859480,
        #             "stats": {volume: 60627.57263769, low: 7631.5, high: 8311.5},
        #             "state": "open",
        #             "settlement_price": 7903.21,
        #             "open_interest": 111543850,
        #             "min_price": 7634,
        #             "max_price": 7866.51,
        #             "mark_price": 7750.02,
        #             "last_price": 7750.5,
        #             "instrument_name": "BTC-PERPETUAL",
        #             "index_price": 7748.01,
        #             "funding_8h": 0.0000026,
        #             "current_funding": 0,
        #             "best_bid_price": 7750,
        #             "best_bid_amount": 19470,
        #             "best_ask_price": 7750.5,
        #             "best_ask_amount": 343280
        #         },
        #         "usIn": 1583778859483941,
        #         "usOut": 1583778859484075,
        #         "usDiff": 134,
        #         "testnet": False
        #     }
        #
        result = self.safe_dict(response, 'result')
        return self.parse_ticker(result, market)

    async def fetch_tickers(self, symbols: Strings = None, params={}) -> Tickers:
        """
        fetches price tickers for multiple markets, statistical information calculated over the past 24 hours for each market

        https://docs.deribit.com/#public-get_book_summary_by_currency

        :param str[] [symbols]: unified symbols of the markets to fetch the ticker for, all market tickers are returned if not assigned
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param str [params.code]: *required* the currency code to fetch the tickers for, eg. 'BTC', 'ETH'
        :returns dict: a dictionary of `ticker structures <https://docs.ccxt.com/#/?id=ticker-structure>`
        """
        await self.load_markets()
        symbols = self.market_symbols(symbols)
        code = self.safe_string_2(params, 'code', 'currency')
        type = None
        params = self.omit(params, ['code'])
        if symbols is not None:
            for i in range(0, len(symbols)):
                market = self.market(symbols[i])
                if code is not None and code != market['base']:
                    raise BadRequest(self.id + ' fetchTickers the base currency must be the same for all symbols, self endpoint only supports one base currency at a time. Read more about it here: https://docs.deribit.com/#public-get_book_summary_by_currency')
                if code is None:
                    code = market['base']
                    type = market['type']
        if code is None:
            raise ArgumentsRequired(self.id + ' fetchTickers requires a currency/code(eg: BTC/ETH/USDT) parameter to fetch tickers for')
        currency = self.currency(code)
        request: dict = {
            'currency': currency['id'],
        }
        if type is not None:
            requestType = None
            if type == 'spot':
                requestType = 'spot'
            elif type == 'future' or (type == 'contract'):
                requestType = 'future'
            elif type == 'option':
                requestType = 'option'
            if requestType is not None:
                request['kind'] = requestType
        response = await self.publicGetGetBookSummaryByCurrency(self.extend(request, params))
        #
        #     {
        #         "jsonrpc": "2.0",
        #         "result": [
        #             {
        #                 "volume": 124.1,
        #                 "underlying_price": 7856.445926872601,
        #                 "underlying_index": "SYN.BTC-10MAR20",
        #                 "quote_currency": "USD",
        #                 "open_interest": 121.8,
        #                 "mid_price": 0.01975,
        #                 "mark_price": 0.01984559,
        #                 "low": 0.0095,
        #                 "last": 0.0205,
        #                 "interest_rate": 0,
        #                 "instrument_name": "BTC-10MAR20-7750-C",
        #                 "high": 0.0295,
        #                 "estimated_delivery_price": 7856.29,
        #                 "creation_timestamp": 1583783678366,
        #                 "bid_price": 0.0185,
        #                 "base_currency": "BTC",
        #                 "ask_price": 0.021
        #             },
        #         ],
        #         "usIn": 1583783678361966,
        #         "usOut": 1583783678372069,
        #         "usDiff": 10103,
        #         "testnet": False
        #     }
        #
        result = self.safe_list(response, 'result', [])
        tickers: dict = {}
        for i in range(0, len(result)):
            ticker = self.parse_ticker(result[i])
            symbol = ticker['symbol']
            tickers[symbol] = ticker
        return self.filter_by_array_tickers(tickers, 'symbol', symbols)

    async def fetch_ohlcv(self, symbol: str, timeframe='1m', since: Int = None, limit: Int = None, params={}) -> List[list]:
        """
        fetches historical candlestick data containing the open, high, low, and close price, and the volume of a market

        https://docs.deribit.com/#public-get_tradingview_chart_data

        :param str symbol: unified symbol of the market to fetch OHLCV data for
        :param str timeframe: the length of time each candle represents
        :param int [since]: timestamp in ms of the earliest candle to fetch
        :param int [limit]: the maximum amount of candles to fetch
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param boolean [params.paginate]: whether to paginate the results, set to False by default
        :param int [params.until]: the latest time in ms to fetch ohlcv for
        :returns int[][]: A list of candles ordered, open, high, low, close, volume
        """
        await self.load_markets()
        paginate = False
        paginate, params = self.handle_option_and_params(params, 'fetchOHLCV', 'paginate')
        if paginate:
            return await self.fetch_paginated_call_deterministic('fetchOHLCV', symbol, since, limit, timeframe, params, 5000)
        market = self.market(symbol)
        request: dict = {
            'instrument_name': market['id'],
            'resolution': self.safe_string(self.timeframes, timeframe, timeframe),
        }
        duration = self.parse_timeframe(timeframe)
        now = self.milliseconds()
        if since is None:
            if limit is None:
                limit = 1000  # at max, it provides 5000 bars, but we set generous default here
            request['start_timestamp'] = now - (limit - 1) * duration * 1000
            request['end_timestamp'] = now
        else:
            since = max(since - 1, 0)
            request['start_timestamp'] = since
            if limit is None:
                request['end_timestamp'] = now
            else:
                request['end_timestamp'] = self.sum(since, limit * duration * 1000)
        until = self.safe_integer(params, 'until')
        if until is not None:
            params = self.omit(params, 'until')
            request['end_timestamp'] = until
        response = await self.publicGetGetTradingviewChartData(self.extend(request, params))
        #
        #     {
        #         "jsonrpc": "2.0",
        #         "result": {
        #             "volume": [3.6680847969999992, 22.682721123, 3.011587939, 0],
        #             "ticks": [1583916960000, 1583917020000, 1583917080000, 1583917140000],
        #             "status": "ok",
        #             "open": [7834, 7839, 7833.5, 7833],
        #             "low": [7834, 7833.5, 7832.5, 7833],
        #             "high": [7839.5, 7839, 7833.5, 7833],
        #             "cost": [28740, 177740, 23590, 0],
        #             "close": [7839.5, 7833.5, 7833, 7833]
        #         },
        #         "usIn": 1583917166709801,
        #         "usOut": 1583917166710175,
        #         "usDiff": 374,
        #         "testnet": False
        #     }
        #
        result = self.safe_value(response, 'result', {})
        ohlcvs = self.convert_trading_view_to_ohlcv(result, 'ticks', 'open', 'high', 'low', 'close', 'volume', True)
        return self.parse_ohlcvs(ohlcvs, market, timeframe, since, limit)

    def parse_trade(self, trade: dict, market: Market = None) -> Trade:
        #
        # fetchTrades(public)
        #
        #      {
        #          "trade_seq":132564271,
        #          "trade_id":"195402220",
        #          "timestamp":1639684927932,
        #          "tick_direction":0,
        #          "price":47946.5,
        #          "mark_price":47944.13,
        #          "instrument_name":"BTC-PERPETUAL",
        #          "index_price":47925.45,
        #          "direction":"buy",
        #          "amount":580.0
        #      }
        #
        #
        # fetchMyTrades, fetchOrderTrades(private)
        #
        #     {
        #         "trade_seq": 3,
        #         "trade_id": "ETH-34066",
        #         "timestamp": 1550219814585,
        #         "tick_direction": 1,
        #         "state": "open",
        #         "self_trade": False,
        #         "reduce_only": False,
        #         "price": 0.04,
        #         "post_only": False,
        #         "order_type": "limit",
        #         "order_id": "ETH-334607",
        #         "matching_id": null,
        #         "liquidity": "M",
        #         "iv": 56.83,
        #         "instrument_name": "ETH-22FEB19-120-C",
        #         "index_price": 121.37,
        #         "fee_currency": "ETH",
        #         "fee": 0.0011,
        #         "direction": "buy",
        #         "amount": 11
        #     }
        #
        id = self.safe_string(trade, 'trade_id')
        marketId = self.safe_string(trade, 'instrument_name')
        symbol = self.safe_symbol(marketId, market)
        timestamp = self.safe_integer(trade, 'timestamp')
        side = self.safe_string(trade, 'direction')
        priceString = self.safe_string(trade, 'price')
        market = self.safe_market(marketId, market)
        # Amount for inverse perpetual and futures is in USD which in ccxt is the cost
        # For options amount and linear is in corresponding cryptocurrency contracts, e.g., BTC or ETH
        amount = self.safe_string(trade, 'amount')
        cost = Precise.string_mul(amount, priceString)
        if market['inverse']:
            cost = Precise.string_div(amount, priceString)
        liquidity = self.safe_string(trade, 'liquidity')
        takerOrMaker = None
        if liquidity is not None:
            # M = maker, T = taker, MT = both
            takerOrMaker = 'maker' if (liquidity == 'M') else 'taker'
        feeCostString = self.safe_string(trade, 'fee')
        fee = None
        if feeCostString is not None:
            feeCurrencyId = self.safe_string(trade, 'fee_currency')
            feeCurrencyCode = self.safe_currency_code(feeCurrencyId)
            fee = {
                'cost': feeCostString,
                'currency': feeCurrencyCode,
            }
        return self.safe_trade({
            'id': id,
            'info': trade,
            'timestamp': timestamp,
            'datetime': self.iso8601(timestamp),
            'symbol': symbol,
            'order': self.safe_string(trade, 'order_id'),
            'type': self.safe_string(trade, 'order_type'),
            'side': side,
            'takerOrMaker': takerOrMaker,
            'price': priceString,
            'amount': amount,
            'cost': cost,
            'fee': fee,
        }, market)

    async def fetch_trades(self, symbol: str, since: Int = None, limit: Int = None, params={}) -> List[Trade]:
        """

        https://docs.deribit.com/#public-get_last_trades_by_instrument
        https://docs.deribit.com/#public-get_last_trades_by_instrument_and_time

        get the list of most recent trades for a particular symbol.
        :param str symbol: unified symbol of the market to fetch trades for
        :param int [since]: timestamp in ms of the earliest trade to fetch
        :param int [limit]: the maximum amount of trades to fetch
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param int [params.until]: the latest time in ms to fetch trades for
        :returns Trade[]: a list of `trade structures <https://docs.ccxt.com/#/?id=public-trades>`
        """
        await self.load_markets()
        market = self.market(symbol)
        request: dict = {
            'instrument_name': market['id'],
            'include_old': True,
        }
        if since is not None:
            request['start_timestamp'] = since
        if limit is not None:
            request['count'] = min(limit, 1000)  # default 10
        until = self.safe_integer_2(params, 'until', 'end_timestamp')
        if until is not None:
            params = self.omit(params, ['until'])
            request['end_timestamp'] = until
        response = None
        if (since is None) and not ('end_timestamp' in request):
            response = await self.publicGetGetLastTradesByInstrument(self.extend(request, params))
        else:
            response = await self.publicGetGetLastTradesByInstrumentAndTime(self.extend(request, params))
        #
        #      {
        #          "jsonrpc":"2.0",
        #          "result": {
        #              "trades": [
        #                  {
        #                      "trade_seq":132564271,
        #                      "trade_id":"195402220",
        #                      "timestamp":1639684927932,
        #                      "tick_direction":0,
        #                      "price":47946.5,
        #                      "mark_price":47944.13,
        #                      "instrument_name":"BTC-PERPETUAL",
        #                      "index_price":47925.45,
        #                      "direction":"buy","amount":580.0
        #                  }
        #              ],
        #              "has_more":true
        #          },
        #          "usIn":****************,
        #          "usOut":****************,
        #          "usDiff":666,
        #          "testnet":false
        #      }
        #
        result = self.safe_value(response, 'result', {})
        trades = self.safe_list(result, 'trades', [])
        return self.parse_trades(trades, market, since, limit)

    async def fetch_trading_fees(self, params={}) -> TradingFees:
        """
        fetch the trading fees for multiple markets

        https://docs.deribit.com/#private-get_account_summary

        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a dictionary of `fee structures <https://docs.ccxt.com/#/?id=fee-structure>` indexed by market symbols
        """
        await self.load_markets()
        code = self.code_from_options('fetchTradingFees', params)
        currency = self.currency(code)
        request: dict = {
            'currency': currency['id'],
            'extended': True,
        }
        response = await self.privateGetGetAccountSummary(self.extend(request, params))
        #
        #     {
        #         "jsonrpc": "2.0",
        #         "result": {
        #             "total_pl": 0,
        #             "session_upl": 0,
        #             "session_rpl": 0,
        #             "session_funding": 0,
        #             "portfolio_margining_enabled": False,
        #             "options_vega": 0,
        #             "options_theta": 0,
        #             "options_session_upl": 0,
        #             "options_session_rpl": 0,
        #             "options_pl": 0,
        #             "options_gamma": 0,
        #             "options_delta": 0,
        #             "margin_balance": 0.********,
        #             "maintenance_margin": 0,
        #             "limits": {
        #                 "non_matching_engine_burst": 300,
        #                 "non_matching_engine": 200,
        #                 "matching_engine_burst": 20,
        #                 "matching_engine": 2
        #             },
        #             "initial_margin": 0,
        #             "futures_session_upl": 0,
        #             "futures_session_rpl": 0,
        #             "futures_pl": 0,
        #             "equity": 0.********,
        #             "deposit_address": "**********************************",
        #             "delta_total": 0,
        #             "currency": "BTC",
        #             "balance": 0.********,
        #             "available_withdrawal_funds": 0.********,
        #             "available_funds": 0.********,
        #             "fees": [
        #                 "currency": '',
        #                 "instrument_type": "perpetual",
        #                 "fee_type": "relative",
        #                 "maker_fee": 0,
        #                 "taker_fee": 0,
        #             ],
        #         },
        #         "usIn": 1583775838115975,
        #         "usOut": 1583775838116520,
        #         "usDiff": 545,
        #         "testnet": False
        #     }
        #
        result = self.safe_value(response, 'result', {})
        fees = self.safe_value(result, 'fees', [])
        perpetualFee: dict = {}
        futureFee: dict = {}
        optionFee: dict = {}
        for i in range(0, len(fees)):
            fee = fees[i]
            instrumentType = self.safe_string(fee, 'instrument_type')
            if instrumentType == 'future':
                futureFee = {
                    'info': fee,
                    'maker': self.safe_number(fee, 'maker_fee'),
                    'taker': self.safe_number(fee, 'taker_fee'),
                }
            elif instrumentType == 'perpetual':
                perpetualFee = {
                    'info': fee,
                    'maker': self.safe_number(fee, 'maker_fee'),
                    'taker': self.safe_number(fee, 'taker_fee'),
                }
            elif instrumentType == 'option':
                optionFee = {
                    'info': fee,
                    'maker': self.safe_number(fee, 'maker_fee'),
                    'taker': self.safe_number(fee, 'taker_fee'),
                }
        parsedFees: dict = {}
        for i in range(0, len(self.symbols)):
            symbol = self.symbols[i]
            market = self.market(symbol)
            fee: dict = {
                'info': market,
                'symbol': symbol,
                'percentage': True,
                'tierBased': True,
                'maker': market['maker'],
                'taker': market['taker'],
            }
            if market['swap']:
                fee = self.extend(fee, perpetualFee)
            elif market['future']:
                fee = self.extend(fee, futureFee)
            elif market['option']:
                fee = self.extend(fee, optionFee)
            parsedFees[symbol] = fee
        return parsedFees

    async def fetch_order_book(self, symbol: str, limit: Int = None, params={}) -> OrderBook:
        """
        fetches information on open orders with bid(buy) and ask(sell) prices, volumes and other data

        https://docs.deribit.com/#public-get_order_book

        :param str symbol: unified symbol of the market to fetch the order book for
        :param int [limit]: the maximum amount of order book entries to return
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: A dictionary of `order book structures <https://docs.ccxt.com/#/?id=order-book-structure>` indexed by market symbols
        """
        await self.load_markets()
        market = self.market(symbol)
        request: dict = {
            'instrument_name': market['id'],
        }
        if limit is not None:
            request['depth'] = limit
        response = await self.publicGetGetOrderBook(self.extend(request, params))
        #
        #     {
        #         "jsonrpc": "2.0",
        #         "result": {
        #             "timestamp": 1583781354740,
        #             "stats": {volume: 61249.66735634, low: 7631.5, high: 8311.5},
        #             "state": "open",
        #             "settlement_price": 7903.21,
        #             "open_interest": 111536690,
        #             "min_price": 7695.13,
        #             "max_price": 7929.49,
        #             "mark_price": 7813.06,
        #             "last_price": 7814.5,
        #             "instrument_name": "BTC-PERPETUAL",
        #             "index_price": 7810.12,
        #             "funding_8h": 0.0000031,
        #             "current_funding": 0,
        #             "change_id": 17538025952,
        #             "bids": [
        #                 [7814, 351820],
        #                 [7813.5, 207490],
        #                 [7813, 32160],
        #             ],
        #             "best_bid_price": 7814,
        #             "best_bid_amount": 351820,
        #             "best_ask_price": 7814.5,
        #             "best_ask_amount": 11880,
        #             "asks": [
        #                 [7814.5, 11880],
        #                 [7815, 18100],
        #                 [7815.5, 2640],
        #             ],
        #         },
        #         "usIn": 1583781354745804,
        #         "usOut": 1583781354745932,
        #         "usDiff": 128,
        #         "testnet": False
        #     }
        #
        result = self.safe_value(response, 'result', {})
        timestamp = self.safe_integer(result, 'timestamp')
        nonce = self.safe_integer(result, 'change_id')
        orderbook = self.parse_order_book(result, market['symbol'], timestamp)
        orderbook['nonce'] = nonce
        return orderbook

    def parse_order_status(self, status: Str):
        statuses: dict = {
            'open': 'open',
            'cancelled': 'canceled',
            'filled': 'closed',
            'rejected': 'rejected',
            'untriggered': 'open',
        }
        return self.safe_string(statuses, status, status)

    def parse_time_in_force(self, timeInForce: Str):
        timeInForces: dict = {
            'good_til_cancelled': 'GTC',
            'fill_or_kill': 'FOK',
            'immediate_or_cancel': 'IOC',
        }
        return self.safe_string(timeInForces, timeInForce, timeInForce)

    def parse_order_type(self, orderType):
        orderTypes: dict = {
            'stop_limit': 'limit',
            'take_limit': 'limit',
            'stop_market': 'market',
            'take_market': 'market',
        }
        return self.safe_string(orderTypes, orderType, orderType)

    def parse_order(self, order: dict, market: Market = None) -> Order:
        #
        # createOrder
        #
        #     {
        #         "time_in_force": "good_til_cancelled",
        #         "reduce_only": False,
        #         "profit_loss": 0,
        #         "price": "market_price",
        #         "post_only": False,
        #         "order_type": "market",
        #         "order_state": "filled",
        #         "order_id": "ETH-349249",
        #         "max_show": 40,
        #         "last_update_timestamp": 1550657341322,
        #         "label": "market0000234",
        #         "is_liquidation": False,
        #         "instrument_name": "ETH-PERPETUAL",
        #         "filled_amount": 40,
        #         "direction": "buy",
        #         "creation_timestamp": 1550657341322,
        #         "commission": 0.000139,
        #         "average_price": 143.81,
        #         "api": True,
        #         "amount": 40,
        #         "trades": [],  # injected by createOrder
        #     }
        #
        marketId = self.safe_string(order, 'instrument_name')
        market = self.safe_market(marketId, market)
        timestamp = self.safe_integer(order, 'creation_timestamp')
        lastUpdate = self.safe_integer(order, 'last_update_timestamp')
        id = self.safe_string(order, 'order_id')
        priceString = self.safe_string(order, 'price')
        if priceString == 'market_price':
            priceString = None
        averageString = self.safe_string(order, 'average_price')
        # Inverse contracts amount is in USD which in ccxt is the cost
        # For options and Linear contracts amount is in corresponding cryptocurrency, e.g., BTC or ETH
        filledString = self.safe_string(order, 'filled_amount')
        amount = self.safe_string(order, 'amount')
        cost = Precise.string_mul(filledString, averageString)
        if self.safe_bool(market, 'inverse'):
            if averageString != '0':
                cost = Precise.string_div(amount, averageString)
        lastTradeTimestamp = None
        if filledString is not None:
            isFilledPositive = Precise.string_gt(filledString, '0')
            if isFilledPositive:
                lastTradeTimestamp = lastUpdate
        status = self.parse_order_status(self.safe_string(order, 'order_state'))
        side = self.safe_string_lower(order, 'direction')
        feeCostString = self.safe_string(order, 'commission')
        fee = None
        if feeCostString is not None:
            feeCostString = Precise.string_abs(feeCostString)
            fee = {
                'cost': feeCostString,
                'currency': market['base'],
            }
        rawType = self.safe_string(order, 'order_type')
        type = self.parse_order_type(rawType)
        # injected in createOrder
        trades = self.safe_value(order, 'trades')
        timeInForce = self.parse_time_in_force(self.safe_string(order, 'time_in_force'))
        postOnly = self.safe_value(order, 'post_only')
        return self.safe_order({
            'info': order,
            'id': id,
            'clientOrderId': None,
            'timestamp': timestamp,
            'datetime': self.iso8601(timestamp),
            'lastTradeTimestamp': lastTradeTimestamp,
            'symbol': market['symbol'],
            'type': type,
            'timeInForce': timeInForce,
            'postOnly': postOnly,
            'side': side,
            'price': priceString,
            'triggerPrice': self.safe_value(order, 'stop_price'),
            'amount': amount,
            'cost': cost,
            'average': averageString,
            'filled': filledString,
            'remaining': None,
            'status': status,
            'fee': fee,
            'trades': trades,
        }, market)

    async def fetch_order(self, id: str, symbol: Str = None, params={}):
        """
        fetches information on an order made by the user

        https://docs.deribit.com/#private-get_order_state

        :param str id: order id
        :param str symbol: unified symbol of the market the order was made in
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: An `order structure <https://docs.ccxt.com/#/?id=order-structure>`
        """
        await self.load_markets()
        request: dict = {
            'order_id': id,
        }
        market = None
        if symbol is not None:
            market = self.market(symbol)
        response = await self.privateGetGetOrderState(self.extend(request, params))
        #
        #     {
        #         "jsonrpc": "2.0",
        #         "id": 4316,
        #         "result": {
        #             "time_in_force": "good_til_cancelled",
        #             "reduce_only": False,
        #             "profit_loss": 0.051134,
        #             "price": 118.94,
        #             "post_only": False,
        #             "order_type": "limit",
        #             "order_state": "filled",
        #             "order_id": "ETH-331562",
        #             "max_show": 37,
        #             "last_update_timestamp": 1550219810944,
        #             "label": "",
        #             "is_liquidation": False,
        #             "instrument_name": "ETH-PERPETUAL",
        #             "filled_amount": 37,
        #             "direction": "sell",
        #             "creation_timestamp": 1550219749176,
        #             "commission": 0.000031,
        #             "average_price": 118.94,
        #             "api": False,
        #             "amount": 37
        #         }
        #     }
        #
        result = self.safe_dict(response, 'result')
        return self.parse_order(result, market)

    async def create_order(self, symbol: str, type: OrderType, side: OrderSide, amount: float, price: Num = None, params={}):
        """
        create a trade order

        https://docs.deribit.com/#private-buy
        https://docs.deribit.com/#private-sell

        :param str symbol: unified symbol of the market to create an order in
        :param str type: 'market' or 'limit'
        :param str side: 'buy' or 'sell'
        :param float amount: how much you want to trade in units of the base currency. For perpetual and inverse futures the amount is in USD units. For options it is in the underlying assets base currency.
        :param float [price]: the price at which the order is to be fulfilled, in units of the quote currency, ignored in market orders
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param str [params.trigger]: the trigger type 'index_price', 'mark_price', or 'last_price', default is 'last_price'
        :param float [params.trailingAmount]: the quote amount to trail away from the current market price
        :returns dict: an `order structure <https://docs.ccxt.com/#/?id=order-structure>`
        """
        await self.load_markets()
        market = self.market(symbol)
        request: dict = {
            'instrument_name': market['id'],
            'amount': self.amount_to_precision(symbol, amount),
            'type': type,  # limit, stop_limit, market, stop_market, default is limit
            # 'label': 'string',  # user-defined label for the order(maximum 64 characters)
            # 'price': self.price_to_precision(symbol, 123.45),  # only for limit and stop_limit orders
            # 'time_in_force' : 'good_til_cancelled',  # fill_or_kill, immediate_or_cancel
            # 'max_show': 123.45,  # max amount within an order to be shown to other customers, 0 for invisible order
            # 'post_only': False,  # if the new price would cause the order to be filled immediately(as taker), the price will be changed to be just below the spread.
            # 'reject_post_only': False,  # if True the order is put to order book unmodified or request is rejected
            # 'reduce_only': False,  # if True, the order is intended to only reduce a current position
            # 'stop_price': False,  # stop price, required for stop_limit orders
            # 'trigger': 'index_price',  # mark_price, last_price, required for stop_limit orders
            # 'advanced': 'usd',  # 'implv', advanced option order type, options only
        }
        trigger = self.safe_string(params, 'trigger', 'last_price')
        timeInForce = self.safe_string_upper(params, 'timeInForce')
        reduceOnly = self.safe_value_2(params, 'reduceOnly', 'reduce_only')
        # only stop loss sell orders are allowed when price crossed from above
        stopLossPrice = self.safe_value(params, 'stopLossPrice')
        # only take profit buy orders are allowed when price crossed from below
        takeProfitPrice = self.safe_value(params, 'takeProfitPrice')
        trailingAmount = self.safe_string_2(params, 'trailingAmount', 'trigger_offset')
        isTrailingAmountOrder = trailingAmount is not None
        isStopLimit = type == 'stop_limit'
        isStopMarket = type == 'stop_market'
        isTakeLimit = type == 'take_limit'
        isTakeMarket = type == 'take_market'
        isStopLossOrder = isStopLimit or isStopMarket or (stopLossPrice is not None)
        isTakeProfitOrder = isTakeLimit or isTakeMarket or (takeProfitPrice is not None)
        if isStopLossOrder and isTakeProfitOrder:
            raise InvalidOrder(self.id + ' createOrder() only allows one of stopLossPrice or takeProfitPrice to be specified')
        isStopOrder = isStopLossOrder or isTakeProfitOrder
        isLimitOrder = (type == 'limit') or isStopLimit or isTakeLimit
        isMarketOrder = (type == 'market') or isStopMarket or isTakeMarket
        exchangeSpecificPostOnly = self.safe_value(params, 'post_only')
        postOnly = self.is_post_only(isMarketOrder, exchangeSpecificPostOnly, params)
        if isLimitOrder:
            request['type'] = 'limit'
            request['price'] = self.price_to_precision(symbol, price)
        else:
            request['type'] = 'market'
        if isTrailingAmountOrder:
            request['trigger'] = trigger
            request['type'] = 'trailing_stop'
            request['trigger_offset'] = self.parse_to_numeric(trailingAmount)
        elif isStopOrder:
            triggerPrice = stopLossPrice if (stopLossPrice is not None) else takeProfitPrice
            request['trigger_price'] = self.price_to_precision(symbol, triggerPrice)
            request['trigger'] = trigger
            if isStopLossOrder:
                if isMarketOrder:
                    # stop_market(sell only)
                    request['type'] = 'stop_market'
                else:
                    # stop_limit(sell only)
                    request['type'] = 'stop_limit'
            else:
                if isMarketOrder:
                    # take_market(buy only)
                    request['type'] = 'take_market'
                else:
                    # take_limit(buy only)
                    request['type'] = 'take_limit'
        if reduceOnly:
            request['reduce_only'] = True
        if postOnly:
            request['post_only'] = True
            request['reject_post_only'] = True
        if timeInForce is not None:
            if timeInForce == 'GTC':
                request['time_in_force'] = 'good_til_cancelled'
            if timeInForce == 'IOC':
                request['time_in_force'] = 'immediate_or_cancel'
            if timeInForce == 'FOK':
                request['time_in_force'] = 'fill_or_kill'
        params = self.omit(params, ['timeInForce', 'stopLossPrice', 'takeProfitPrice', 'postOnly', 'reduceOnly', 'trailingAmount'])
        response = None
        if self.capitalize(side) == 'Buy':
            response = await self.privateGetBuy(self.extend(request, params))
        else:
            response = await self.privateGetSell(self.extend(request, params))
        #
        #     {
        #         "jsonrpc": "2.0",
        #         "id": 5275,
        #         "result": {
        #             "trades": [
        #                 {
        #                     "trade_seq": 14151,
        #                     "trade_id": "ETH-37435",
        #                     "timestamp": 1550657341322,
        #                     "tick_direction": 2,
        #                     "state": "closed",
        #                     "self_trade": False,
        #                     "price": 143.81,
        #                     "order_type": "market",
        #                     "order_id": "ETH-349249",
        #                     "matching_id": null,
        #                     "liquidity": "T",
        #                     "label": "market0000234",
        #                     "instrument_name": "ETH-PERPETUAL",
        #                     "index_price": 143.73,
        #                     "fee_currency": "ETH",
        #                     "fee": 0.000139,
        #                     "direction": "buy",
        #                     "amount": 40
        #                 }
        #             ],
        #             "order": {
        #                 "time_in_force": "good_til_cancelled",
        #                 "reduce_only": False,
        #                 "profit_loss": 0,
        #                 "price": "market_price",
        #                 "post_only": False,
        #                 "order_type": "market",
        #                 "order_state": "filled",
        #                 "order_id": "ETH-349249",
        #                 "max_show": 40,
        #                 "last_update_timestamp": 1550657341322,
        #                 "label": "market0000234",
        #                 "is_liquidation": False,
        #                 "instrument_name": "ETH-PERPETUAL",
        #                 "filled_amount": 40,
        #                 "direction": "buy",
        #                 "creation_timestamp": 1550657341322,
        #                 "commission": 0.000139,
        #                 "average_price": 143.81,
        #                 "api": True,
        #                 "amount": 40
        #             }
        #         }
        #     }
        #
        result = self.safe_value(response, 'result', {})
        order = self.safe_value(result, 'order')
        trades = self.safe_value(result, 'trades', [])
        order['trades'] = trades
        return self.parse_order(order, market)

    async def edit_order(self, id: str, symbol: str, type: OrderType, side: OrderSide, amount: Num = None, price: Num = None, params={}):
        """
        edit a trade order

        https://docs.deribit.com/#private-edit

        :param str id: edit order id
        :param str [symbol]: unified symbol of the market to edit an order in
        :param str [type]: 'market' or 'limit'
        :param str [side]: 'buy' or 'sell'
        :param float amount: how much you want to trade in units of the base currency. For perpetual and inverse futures the amount is in USD units. For options it is in the underlying assets base currency.
        :param float [price]: the price at which the order is to be fulfilled, in units of the quote currency, ignored in market orders
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param float [params.trailingAmount]: the quote amount to trail away from the current market price
        :returns dict: an `order structure <https://docs.ccxt.com/#/?id=order-structure>`
        """
        if amount is None:
            raise ArgumentsRequired(self.id + ' editOrder() requires an amount argument')
        await self.load_markets()
        request: dict = {
            'order_id': id,
            'amount': self.amount_to_precision(symbol, amount),
            # 'post_only': False,  # if the new price would cause the order to be filled immediately(as taker), the price will be changed to be just below the spread.
            # 'reject_post_only': False,  # if True the order is put to order book unmodified or request is rejected
            # 'reduce_only': False,  # if True, the order is intended to only reduce a current position
            # 'stop_price': False,  # stop price, required for stop_limit orders
            # 'advanced': 'usd',  # 'implv', advanced option order type, options only
        }
        if price is not None:
            request['price'] = self.price_to_precision(symbol, price)
        trailingAmount = self.safe_string_2(params, 'trailingAmount', 'trigger_offset')
        isTrailingAmountOrder = trailingAmount is not None
        if isTrailingAmountOrder:
            request['trigger_offset'] = self.parse_to_numeric(trailingAmount)
            params = self.omit(params, 'trigger_offset')
        response = await self.privateGetEdit(self.extend(request, params))
        result = self.safe_value(response, 'result', {})
        order = self.safe_value(result, 'order')
        trades = self.safe_value(result, 'trades', [])
        order['trades'] = trades
        return self.parse_order(order)

    async def cancel_order(self, id: str, symbol: Str = None, params={}):
        """
        cancels an open order

        https://docs.deribit.com/#private-cancel

        :param str id: order id
        :param str symbol: not used by deribit cancelOrder()
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: An `order structure <https://docs.ccxt.com/#/?id=order-structure>`
        """
        await self.load_markets()
        request: dict = {
            'order_id': id,
        }
        response = await self.privateGetCancel(self.extend(request, params))
        result = self.safe_dict(response, 'result', {})
        return self.parse_order(result)

    async def cancel_all_orders(self, symbol: Str = None, params={}):
        """
        cancel all open orders

        https://docs.deribit.com/#private-cancel_all
        https://docs.deribit.com/#private-cancel_all_by_instrument

        :param str symbol: unified market symbol, only orders in the market of self symbol are cancelled when symbol is not None
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: a list of `order structures <https://docs.ccxt.com/#/?id=order-structure>`
        """
        await self.load_markets()
        request: dict = {}
        response = None
        if symbol is None:
            response = await self.privateGetCancelAll(self.extend(request, params))
        else:
            market = self.market(symbol)
            request['instrument_name'] = market['id']
            response = await self.privateGetCancelAllByInstrument(self.extend(request, params))
        #
        #    {
        #        jsonrpc: '2.0',
        #        result: '1',
        #        usIn: '1720508354127369',
        #        usOut: '1720508354133603',
        #        usDiff: '6234',
        #        testnet: True
        #    }
        #
        return [
            self.safe_order({
                'info': response,
            }),
        ]

    async def fetch_open_orders(self, symbol: Str = None, since: Int = None, limit: Int = None, params={}) -> List[Order]:
        """
        fetch all unfilled currently open orders

        https://docs.deribit.com/#private-get_open_orders_by_currency
        https://docs.deribit.com/#private-get_open_orders_by_instrument

        :param str symbol: unified market symbol
        :param int [since]: the earliest time in ms to fetch open orders for
        :param int [limit]: the maximum number of  open orders structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns Order[]: a list of `order structures <https://docs.ccxt.com/#/?id=order-structure>`
        """
        await self.load_markets()
        request: dict = {}
        market = None
        response = None
        if symbol is None:
            code = self.code_from_options('fetchOpenOrders', params)
            currency = self.currency(code)
            request['currency'] = currency['id']
            response = await self.privateGetGetOpenOrdersByCurrency(self.extend(request, params))
        else:
            market = self.market(symbol)
            request['instrument_name'] = market['id']
            response = await self.privateGetGetOpenOrdersByInstrument(self.extend(request, params))
        result = self.safe_list(response, 'result', [])
        return self.parse_orders(result, market, since, limit)

    async def fetch_closed_orders(self, symbol: Str = None, since: Int = None, limit: Int = None, params={}) -> List[Order]:
        """
        fetches information on multiple closed orders made by the user

        https://docs.deribit.com/#private-get_order_history_by_currency
        https://docs.deribit.com/#private-get_order_history_by_instrument

        :param str symbol: unified market symbol of the market orders were made in
        :param int [since]: the earliest time in ms to fetch orders for
        :param int [limit]: the maximum number of order structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns Order[]: a list of `order structures <https://docs.ccxt.com/#/?id=order-structure>`
        """
        await self.load_markets()
        request: dict = {}
        market = None
        response = None
        if symbol is None:
            code = self.code_from_options('fetchClosedOrders', params)
            currency = self.currency(code)
            request['currency'] = currency['id']
            response = await self.privateGetGetOrderHistoryByCurrency(self.extend(request, params))
        else:
            market = self.market(symbol)
            request['instrument_name'] = market['id']
            response = await self.privateGetGetOrderHistoryByInstrument(self.extend(request, params))
        result = self.safe_list(response, 'result', [])
        return self.parse_orders(result, market, since, limit)

    async def fetch_order_trades(self, id: str, symbol: Str = None, since: Int = None, limit: Int = None, params={}):
        """
        fetch all the trades made from a single order

        https://docs.deribit.com/#private-get_user_trades_by_order

        :param str id: order id
        :param str symbol: unified market symbol
        :param int [since]: the earliest time in ms to fetch trades for
        :param int [limit]: the maximum number of trades to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: a list of `trade structures <https://docs.ccxt.com/#/?id=trade-structure>`
        """
        await self.load_markets()
        request: dict = {
            'order_id': id,
        }
        response = await self.privateGetGetUserTradesByOrder(self.extend(request, params))
        #
        #     {
        #         "jsonrpc": "2.0",
        #         "id": 9367,
        #         "result": {
        #             "trades": [
        #                 {
        #                     "trade_seq": 3,
        #                     "trade_id": "ETH-34066",
        #                     "timestamp": 1550219814585,
        #                     "tick_direction": 1,
        #                     "state": "open",
        #                     "self_trade": False,
        #                     "reduce_only": False,
        #                     "price": 0.04,
        #                     "post_only": False,
        #                     "order_type": "limit",
        #                     "order_id": "ETH-334607",
        #                     "matching_id": null,
        #                     "liquidity": "M",
        #                     "iv": 56.83,
        #                     "instrument_name": "ETH-22FEB19-120-C",
        #                     "index_price": 121.37,
        #                     "fee_currency": "ETH",
        #                     "fee": 0.0011,
        #                     "direction": "buy",
        #                     "amount": 11
        #                 },
        #             ],
        #             "has_more": True
        #         }
        #     }
        #
        result = self.safe_list(response, 'result', [])
        return self.parse_trades(result, None, since, limit)

    async def fetch_my_trades(self, symbol: Str = None, since: Int = None, limit: Int = None, params={}):
        """
        fetch all trades made by the user

        https://docs.deribit.com/#private-get_user_trades_by_currency
        https://docs.deribit.com/#private-get_user_trades_by_currency_and_time
        https://docs.deribit.com/#private-get_user_trades_by_instrument
        https://docs.deribit.com/#private-get_user_trades_by_instrument_and_time

        :param str symbol: unified market symbol
        :param int [since]: the earliest time in ms to fetch trades for
        :param int [limit]: the maximum number of trades structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns Trade[]: a list of `trade structures <https://docs.ccxt.com/#/?id=trade-structure>`
        """
        await self.load_markets()
        request: dict = {
            'include_old': True,
        }
        market = None
        if limit is not None:
            request['count'] = limit  # default 10
        response = None
        if symbol is None:
            code = self.code_from_options('fetchMyTrades', params)
            currency = self.currency(code)
            request['currency'] = currency['id']
            if since is None:
                response = await self.privateGetGetUserTradesByCurrency(self.extend(request, params))
            else:
                request['start_timestamp'] = since
                response = await self.privateGetGetUserTradesByCurrencyAndTime(self.extend(request, params))
        else:
            market = self.market(symbol)
            request['instrument_name'] = market['id']
            if since is None:
                response = await self.privateGetGetUserTradesByInstrument(self.extend(request, params))
            else:
                request['start_timestamp'] = since
                response = await self.privateGetGetUserTradesByInstrumentAndTime(self.extend(request, params))
        #
        #     {
        #         "jsonrpc": "2.0",
        #         "id": 9367,
        #         "result": {
        #             "trades": [
        #                 {
        #                     "trade_seq": 3,
        #                     "trade_id": "ETH-34066",
        #                     "timestamp": 1550219814585,
        #                     "tick_direction": 1,
        #                     "state": "open",
        #                     "self_trade": False,
        #                     "reduce_only": False,
        #                     "price": 0.04,
        #                     "post_only": False,
        #                     "order_type": "limit",
        #                     "order_id": "ETH-334607",
        #                     "matching_id": null,
        #                     "liquidity": "M",
        #                     "iv": 56.83,
        #                     "instrument_name": "ETH-22FEB19-120-C",
        #                     "index_price": 121.37,
        #                     "fee_currency": "ETH",
        #                     "fee": 0.0011,
        #                     "direction": "buy",
        #                     "amount": 11
        #                 },
        #             ],
        #             "has_more": True
        #         }
        #     }
        #
        result = self.safe_value(response, 'result', {})
        trades = self.safe_list(result, 'trades', [])
        return self.parse_trades(trades, market, since, limit)

    async def fetch_deposits(self, code: Str = None, since: Int = None, limit: Int = None, params={}) -> List[Transaction]:
        """
        fetch all deposits made to an account

        https://docs.deribit.com/#private-get_deposits

        :param str code: unified currency code
        :param int [since]: the earliest time in ms to fetch deposits for
        :param int [limit]: the maximum number of deposits structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: a list of `transaction structures <https://docs.ccxt.com/#/?id=transaction-structure>`
        """
        if code is None:
            raise ArgumentsRequired(self.id + ' fetchDeposits() requires a currency code argument')
        await self.load_markets()
        currency = self.currency(code)
        request: dict = {
            'currency': currency['id'],
        }
        if limit is not None:
            request['count'] = limit
        response = await self.privateGetGetDeposits(self.extend(request, params))
        #
        #     {
        #         "jsonrpc": "2.0",
        #         "id": 5611,
        #         "result": {
        #             "count": 1,
        #             "data": [
        #                 {
        #                     "address": "2N35qDKDY22zmJq9eSyiAerMD4enJ1xx6ax",
        #                     "amount": 5,
        #                     "currency": "BTC",
        #                     "received_timestamp": 1549295017670,
        #                     "state": "completed",
        #                     "transaction_id": "230669110fdaf0a0dbcdc079b6b8b43d5af29cc73683835b9bc6b3406c065fda",
        #                     "updated_timestamp": *************
        #                 }
        #             ]
        #         }
        #     }
        #
        result = self.safe_value(response, 'result', {})
        data = self.safe_list(result, 'data', [])
        return self.parse_transactions(data, currency, since, limit, params)

    async def fetch_withdrawals(self, code: Str = None, since: Int = None, limit: Int = None, params={}) -> List[Transaction]:
        """
        fetch all withdrawals made from an account

        https://docs.deribit.com/#private-get_withdrawals

        :param str code: unified currency code
        :param int [since]: the earliest time in ms to fetch withdrawals for
        :param int [limit]: the maximum number of withdrawals structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: a list of `transaction structures <https://docs.ccxt.com/#/?id=transaction-structure>`
        """
        if code is None:
            raise ArgumentsRequired(self.id + ' fetchWithdrawals() requires a currency code argument')
        await self.load_markets()
        currency = self.currency(code)
        request: dict = {
            'currency': currency['id'],
        }
        if limit is not None:
            request['count'] = limit
        response = await self.privateGetGetWithdrawals(self.extend(request, params))
        #
        #     {
        #         "jsonrpc": "2.0",
        #         "id": 2745,
        #         "result": {
        #             "count": 1,
        #             "data": [
        #                 {
        #                     "address": "2NBqqD5GRJ8wHy1PYyCXTe9ke5226FhavBz",
        #                     "amount": 0.5,
        #                     "confirmed_timestamp": null,
        #                     "created_timestamp": 1550571443070,
        #                     "currency": "BTC",
        #                     "fee": 0.0001,
        #                     "id": 1,
        #                     "priority": 0.15,
        #                     "state": "unconfirmed",
        #                     "transaction_id": null,
        #                     "updated_timestamp": 1550571443070
        #                 }
        #             ]
        #         }
        #     }
        #
        result = self.safe_value(response, 'result', {})
        data = self.safe_list(result, 'data', [])
        return self.parse_transactions(data, currency, since, limit, params)

    def parse_transaction_status(self, status: Str):
        statuses: dict = {
            'completed': 'ok',
            'unconfirmed': 'pending',
        }
        return self.safe_string(statuses, status, status)

    def parse_transaction(self, transaction: dict, currency: Currency = None) -> Transaction:
        #
        # fetchWithdrawals
        #
        #     {
        #         "address": "2NBqqD5GRJ8wHy1PYyCXTe9ke5226FhavBz",
        #         "amount": 0.5,
        #         "confirmed_timestamp": null,
        #         "created_timestamp": 1550571443070,
        #         "currency": "BTC",
        #         "fee": 0.0001,
        #         "id": 1,
        #         "priority": 0.15,
        #         "state": "unconfirmed",
        #         "transaction_id": null,
        #         "updated_timestamp": 1550571443070
        #     }
        #
        # fetchDeposits
        #
        #     {
        #         "address": "2N35qDKDY22zmJq9eSyiAerMD4enJ1xx6ax",
        #         "amount": 5,
        #         "currency": "BTC",
        #         "received_timestamp": 1549295017670,
        #         "state": "completed",
        #         "transaction_id": "230669110fdaf0a0dbcdc079b6b8b43d5af29cc73683835b9bc6b3406c065fda",
        #         "updated_timestamp": *************
        #     }
        #
        currencyId = self.safe_string(transaction, 'currency')
        code = self.safe_currency_code(currencyId, currency)
        timestamp = self.safe_integer_2(transaction, 'created_timestamp', 'received_timestamp')
        updated = self.safe_integer(transaction, 'updated_timestamp')
        status = self.parse_transaction_status(self.safe_string(transaction, 'state'))
        address = self.safe_string(transaction, 'address')
        feeCost = self.safe_number(transaction, 'fee')
        type = 'deposit'
        fee = None
        if feeCost is not None:
            type = 'withdrawal'
            fee = {
                'cost': feeCost,
                'currency': code,
            }
        return {
            'info': transaction,
            'id': self.safe_string(transaction, 'id'),
            'txid': self.safe_string(transaction, 'transaction_id'),
            'timestamp': timestamp,
            'datetime': self.iso8601(timestamp),
            'address': address,
            'addressTo': address,
            'addressFrom': None,
            'tag': None,
            'tagTo': None,
            'tagFrom': None,
            'type': type,
            'amount': self.safe_number(transaction, 'amount'),
            'currency': code,
            'status': status,
            'updated': updated,
            'network': None,
            'internal': None,
            'comment': None,
            'fee': fee,
        }

    def parse_position(self, position: dict, market: Market = None):
        #
        #     {
        #         "jsonrpc": "2.0",
        #         "id": 404,
        #         "result": {
        #             "average_price": 0,
        #             "delta": 0,
        #             "direction": "buy",
        #             "estimated_liquidation_price": 0,
        #             "floating_profit_loss": 0,
        #             "index_price": 3555.86,
        #             "initial_margin": 0,
        #             "instrument_name": "BTC-PERPETUAL",
        #             "leverage": 100,
        #             "kind": "future",
        #             "maintenance_margin": 0,
        #             "mark_price": 3556.62,
        #             "open_orders_margin": 0.000165889,
        #             "realized_profit_loss": 0,
        #             "settlement_price": 3555.44,
        #             "size": 0,
        #             "size_currency": 0,
        #             "total_profit_loss": 0
        #         }
        #     }
        #
        contract = self.safe_string(position, 'instrument_name')
        market = self.safe_market(contract, market)
        side = self.safe_string(position, 'direction')
        side = 'long' if (side == 'buy') else 'short'
        unrealizedPnl = self.safe_string(position, 'floating_profit_loss')
        initialMarginString = self.safe_string(position, 'initial_margin')
        notionalString = self.safe_string(position, 'size_currency')
        maintenanceMarginString = self.safe_string(position, 'maintenance_margin')
        currentTime = self.milliseconds()
        return self.safe_position({
            'info': position,
            'id': None,
            'symbol': self.safe_string(market, 'symbol'),
            'timestamp': currentTime,
            'datetime': self.iso8601(currentTime),
            'lastUpdateTimestamp': None,
            'initialMargin': self.parse_number(initialMarginString),
            'initialMarginPercentage': self.parse_number(Precise.string_mul(Precise.string_div(initialMarginString, notionalString), '100')),
            'maintenanceMargin': self.parse_number(maintenanceMarginString),
            'maintenanceMarginPercentage': self.parse_number(Precise.string_mul(Precise.string_div(maintenanceMarginString, notionalString), '100')),
            'entryPrice': self.safe_number(position, 'average_price'),
            'notional': self.parse_number(notionalString),
            'leverage': self.safe_integer(position, 'leverage'),
            'unrealizedPnl': self.parse_number(unrealizedPnl),
            'contracts': None,
            'contractSize': self.safe_number(market, 'contractSize'),
            'marginRatio': None,
            'liquidationPrice': self.safe_number(position, 'estimated_liquidation_price'),
            'markPrice': self.safe_number(position, 'mark_price'),
            'lastPrice': None,
            'collateral': None,
            'marginMode': None,
            'side': side,
            'percentage': None,
            'hedged': None,
            'stopLossPrice': None,
            'takeProfitPrice': None,
        })

    async def fetch_position(self, symbol: str, params={}):
        """
        fetch data on a single open contract trade position

        https://docs.deribit.com/#private-get_position

        :param str symbol: unified market symbol of the market the position is held in, default is None
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `position structure <https://docs.ccxt.com/#/?id=position-structure>`
        """
        await self.load_markets()
        market = self.market(symbol)
        request: dict = {
            'instrument_name': market['id'],
        }
        response = await self.privateGetGetPosition(self.extend(request, params))
        #
        #     {
        #         "jsonrpc": "2.0",
        #         "id": 404,
        #         "result": {
        #             "average_price": 0,
        #             "delta": 0,
        #             "direction": "buy",
        #             "estimated_liquidation_price": 0,
        #             "floating_profit_loss": 0,
        #             "index_price": 3555.86,
        #             "initial_margin": 0,
        #             "instrument_name": "BTC-PERPETUAL",
        #             "leverage": 100,
        #             "kind": "future",
        #             "maintenance_margin": 0,
        #             "mark_price": 3556.62,
        #             "open_orders_margin": 0.000165889,
        #             "realized_profit_loss": 0,
        #             "settlement_price": 3555.44,
        #             "size": 0,
        #             "size_currency": 0,
        #             "total_profit_loss": 0
        #         }
        #     }
        #
        result = self.safe_dict(response, 'result')
        return self.parse_position(result)

    async def fetch_positions(self, symbols: Strings = None, params={}) -> List[Position]:
        """
        fetch all open positions

        https://docs.deribit.com/#private-get_positions

        :param str[]|None symbols: list of unified market symbols
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param str [params.currency]: currency code filter for positions
        :param str [params.kind]: market type filter for positions 'future', 'option', 'spot', 'future_combo' or 'option_combo'
        :param int [params.subaccount_id]: the user id for the subaccount
        :returns dict[]: a list of `position structure <https://docs.ccxt.com/#/?id=position-structure>`
        """
        await self.load_markets()
        code = self.safe_string(params, 'currency')
        request: dict = {}
        if code is not None:
            params = self.omit(params, 'currency')
            currency = self.currency(code)
            request['currency'] = currency['id']
        response = await self.privateGetGetPositions(self.extend(request, params))
        #
        #     {
        #         "jsonrpc": "2.0",
        #         "id": 2236,
        #         "result": [
        #             {
        #                 "average_price": 7440.18,
        #                 "delta": 0.*********,
        #                 "direction": "buy",
        #                 "estimated_liquidation_price": 1.74,
        #                 "floating_profit_loss": 0,
        #                 "index_price": 7466.79,
        #                 "initial_margin": 0.000197283,
        #                 "instrument_name": "BTC-PERPETUAL",
        #                 "kind": "future",
        #                 "leverage": 34,
        #                 "maintenance_margin": 0.000143783,
        #                 "mark_price": 7476.65,
        #                 "open_orders_margin": 0.000197288,
        #                 "realized_funding": -1e-8,
        #                 "realized_profit_loss": -9e-9,
        #                 "settlement_price": 7476.65,
        #                 "size": 50,
        #                 "size_currency": 0.*********,
        #                 "total_profit_loss": 0.000032781
        #             },
        #         ]
        #     }
        #
        result = self.safe_list(response, 'result')
        return self.parse_positions(result, symbols)

    async def fetch_volatility_history(self, code: str, params={}):
        """
        fetch the historical volatility of an option market based on an underlying asset

        https://docs.deribit.com/#public-get_historical_volatility

        :param str code: unified currency code
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: a list of `volatility history objects <https://docs.ccxt.com/#/?id=volatility-structure>`
        """
        await self.load_markets()
        currency = self.currency(code)
        request: dict = {
            'currency': currency['id'],
        }
        response = await self.publicGetGetHistoricalVolatility(self.extend(request, params))
        #
        #     {
        #         "jsonrpc": "2.0",
        #         "result": [
        #             [1640142000000,63.828320460740585],
        #             [1640142000000,63.828320460740585],
        #             [1640145600000,64.03821964123213]
        #         ],
        #         "usIn": 1641515379467734,
        #         "usOut": 1641515379468095,
        #         "usDiff": 361,
        #         "testnet": False
        #     }
        #
        return self.parse_volatility_history(response)

    def parse_volatility_history(self, volatility):
        #
        #     {
        #         "jsonrpc": "2.0",
        #         "result": [
        #             [1640142000000,63.828320460740585],
        #             [1640142000000,63.828320460740585],
        #             [1640145600000,64.03821964123213]
        #         ],
        #         "usIn": 1641515379467734,
        #         "usOut": 1641515379468095,
        #         "usDiff": 361,
        #         "testnet": False
        #     }
        #
        volatilityResult = self.safe_value(volatility, 'result', [])
        result = []
        for i in range(0, len(volatilityResult)):
            timestamp = self.safe_integer(volatilityResult[i], 0)
            volatilityObj = self.safe_number(volatilityResult[i], 1)
            result.append({
                'info': volatilityObj,
                'timestamp': timestamp,
                'datetime': self.iso8601(timestamp),
                'volatility': volatilityObj,
            })
        return result

    async def fetch_transfers(self, code: Str = None, since: Int = None, limit: Int = None, params={}) -> List[TransferEntry]:
        """
        fetch a history of internal transfers made on an account

        https://docs.deribit.com/#private-get_transfers

        :param str code: unified currency code of the currency transferred
        :param int [since]: the earliest time in ms to fetch transfers for
        :param int [limit]: the maximum number of  transfers structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: a list of `transfer structures <https://docs.ccxt.com/#/?id=transfer-structure>`
        """
        if code is None:
            raise ArgumentsRequired(self.id + ' fetchTransfers() requires a currency code argument')
        await self.load_markets()
        currency = self.currency(code)
        request: dict = {
            'currency': currency['id'],
        }
        if limit is not None:
            request['count'] = limit
        response = await self.privateGetGetTransfers(self.extend(request, params))
        #
        #     {
        #         "jsonrpc": "2.0",
        #         "id": 7606,
        #         "result": {
        #             "count": 2,
        #             "data": [
        #                 {
        #                     "amount": 0.2,
        #                     "created_timestamp": *************,
        #                     "currency": "BTC",
        #                     "direction": "payment",
        #                     "id": 2,
        #                     "other_side": "2MzyQc5Tkik61kJbEpJV5D5H9VfWHZK9Sgy",
        #                     "state": "prepared",
        #                     "type": "user",
        #                     "updated_timestamp": *************
        #                 },
        #                 {
        #                     "amount": 0.3,
        #                     "created_timestamp": *************,
        #                     "currency": "BTC",
        #                     "direction": "payment",
        #                     "id": 1,
        #                     "other_side": "new_user_1_1",
        #                     "state": "confirmed",
        #                     "type": "subaccount",
        #                     "updated_timestamp": *************
        #                 }
        #             ]
        #         }
        #     }
        #
        result = self.safe_value(response, 'result', {})
        transfers = self.safe_list(result, 'data', [])
        return self.parse_transfers(transfers, currency, since, limit, params)

    async def transfer(self, code: str, amount: float, fromAccount: str, toAccount: str, params={}) -> TransferEntry:
        """
        transfer currency internally between wallets on the same account

        https://docs.deribit.com/#private-submit_transfer_to_user
        https://docs.deribit.com/#private-submit_transfer_to_subaccount

        :param str code: unified currency code
        :param float amount: amount to transfer
        :param str fromAccount: account to transfer from
        :param str toAccount: account to transfer to
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `transfer structure <https://docs.ccxt.com/#/?id=transfer-structure>`
        """
        await self.load_markets()
        currency = self.currency(code)
        request: dict = {
            'amount': amount,
            'currency': currency['id'],
            'destination': toAccount,
        }
        method = self.safe_string(params, 'method')
        params = self.omit(params, 'method')
        if method is None:
            transferOptions = self.safe_value(self.options, 'transfer', {})
            method = self.safe_string(transferOptions, 'method', 'privateGetSubmitTransferToSubaccount')
        response = None
        if method == 'privateGetSubmitTransferToUser':
            response = await self.privateGetSubmitTransferToUser(self.extend(request, params))
        else:
            response = await self.privateGetSubmitTransferToSubaccount(self.extend(request, params))
        #
        #     {
        #         "jsonrpc": "2.0",
        #         "id": 9421,
        #         "result": {
        #             "updated_timestamp": *************,
        #             "type": "user",
        #             "state": "prepared",
        #             "other_side": "******************************************",
        #             "id": 3,
        #             "direction": "payment",
        #             "currency": "ETH",
        #             "created_timestamp": *************,
        #             "amount": 13.456
        #         }
        #     }
        #
        result = self.safe_dict(response, 'result', {})
        return self.parse_transfer(result, currency)

    def parse_transfer(self, transfer: dict, currency: Currency = None) -> TransferEntry:
        #
        #     {
        #         "updated_timestamp": *************,
        #         "type": "user",
        #         "state": "prepared",
        #         "other_side": "******************************************",
        #         "id": 3,
        #         "direction": "payment",
        #         "currency": "ETH",
        #         "created_timestamp": *************,
        #         "amount": 13.456
        #     }
        #
        timestamp = self.safe_timestamp(transfer, 'created_timestamp')
        status = self.safe_string(transfer, 'state')
        account = self.safe_string(transfer, 'other_side')
        direction = self.safe_string(transfer, 'direction')
        currencyId = self.safe_string(transfer, 'currency')
        return {
            'info': transfer,
            'id': self.safe_string(transfer, 'id'),
            'status': self.parse_transfer_status(status),
            'amount': self.safe_number(transfer, 'amount'),
            'currency': self.safe_currency_code(currencyId, currency),
            'fromAccount': direction != account if 'payment' else None,
            'toAccount': direction == account if 'payment' else None,
            'timestamp': timestamp,
            'datetime': self.iso8601(timestamp),
        }

    def parse_transfer_status(self, status: Str) -> Str:
        statuses: dict = {
            'prepared': 'pending',
            'confirmed': 'ok',
            'cancelled': 'cancelled',
            'waiting_for_admin': 'pending',
        }
        return self.safe_string(statuses, status, status)

    async def withdraw(self, code: str, amount: float, address: str, tag=None, params={}) -> Transaction:
        """
        make a withdrawal

        https://docs.deribit.com/#private-withdraw

        :param str code: unified currency code
        :param float amount: the amount to withdraw
        :param str address: the address to withdraw to
        :param str tag:
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `transaction structure <https://docs.ccxt.com/#/?id=transaction-structure>`
        """
        tag, params = self.handle_withdraw_tag_and_params(tag, params)
        self.check_address(address)
        await self.load_markets()
        currency = self.currency(code)
        request: dict = {
            'currency': currency['id'],
            'address': address,  # must be in the address book
            'amount': amount,
            # 'priority': 'high',  # low, mid, high, very_high, extreme_high, insane
            # 'tfa': '123456',  # if enabled
        }
        if self.twofa is not None:
            request['tfa'] = self.totp(self.twofa)
        response = await self.privateGetWithdraw(self.extend(request, params))
        return self.parse_transaction(response, currency)

    def parse_deposit_withdraw_fee(self, fee, currency: Currency = None):
        #
        #    {
        #      "withdrawal_priorities": [],
        #      "withdrawal_fee": 0.01457324,
        #      "min_withdrawal_fee": 0.000001,
        #      "min_confirmations": 1,
        #      "fee_precision": 8,
        #      "currency_long": "Solana",
        #      "currency": "SOL",
        #      "coin_type": "SOL"
        #    }
        #
        return {
            'info': fee,
            'withdraw': {
                'fee': self.safe_number(fee, 'withdrawal_fee'),
                'percentage': False,
            },
            'deposit': {
                'fee': None,
                'percentage': None,
            },
            'networks': {},
        }

    async def fetch_deposit_withdraw_fees(self, codes: Strings = None, params={}):
        """
        fetch deposit and withdraw fees

        https://docs.deribit.com/#public-get_currencies

        :param str[]|None codes: list of unified currency codes
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a list of `fee structures <https://docs.ccxt.com/#/?id=fee-structure>`
        """
        await self.load_markets()
        response = await self.publicGetGetCurrencies(params)
        #
        #    {
        #      "jsonrpc": "2.0",
        #      "result": [
        #        {
        #          "withdrawal_priorities": [],
        #          "withdrawal_fee": 0.01457324,
        #          "min_withdrawal_fee": 0.000001,
        #          "min_confirmations": 1,
        #          "fee_precision": 8,
        #          "currency_long": "Solana",
        #          "currency": "SOL",
        #          "coin_type": "SOL"
        #        },
        #        ...
        #      ],
        #      "usIn": 1688652701456124,
        #      "usOut": 1688652701456390,
        #      "usDiff": 266,
        #      "testnet": True
        #    }
        #
        data = self.safe_list(response, 'result', [])
        return self.parse_deposit_withdraw_fees(data, codes, 'currency')

    async def fetch_funding_rate(self, symbol: str, params={}) -> FundingRate:
        """
        fetch the current funding rate

        https://docs.deribit.com/#public-get_funding_rate_value

        :param str symbol: unified market symbol
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param int [params.start_timestamp]: fetch funding rate starting from self timestamp
        :param int [params.end_timestamp]: fetch funding rate ending at self timestamp
        :returns dict: a `funding rate structure <https://docs.ccxt.com/#/?id=funding-rate-structure>`
        """
        await self.load_markets()
        market = self.market(symbol)
        time = self.milliseconds()
        request: dict = {
            'instrument_name': market['id'],
            'start_timestamp': time - (8 * 60 * 60 * 1000),  # 8h ago,
            'end_timestamp': time,
        }
        response = await self.publicGetGetFundingRateValue(self.extend(request, params))
        #
        #   {
        #       "jsonrpc":"2.0",
        #       "result":"0",
        #       "usIn":"1691161645596519",
        #       "usOut":"1691161645597149",
        #       "usDiff":"630",
        #       "testnet":false
        #   }
        #
        return self.parse_funding_rate(response, market)

    async def fetch_funding_rate_history(self, symbol: Str = None, since: Int = None, limit: Int = None, params={}):
        """
        fetch the current funding rate

        https://docs.deribit.com/#public-get_funding_rate_history

        :param str symbol: unified market symbol
        :param int [since]: the earliest time in ms to fetch funding rate history for
        :param int [limit]: the maximum number of entries to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param int [params.until]: fetch funding rate ending at self timestamp
        :param boolean [params.paginate]: default False, when True will automatically paginate by calling self endpoint multiple times. See in the docs all the [availble parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-params)
        :returns dict: a `funding rate structure <https://docs.ccxt.com/#/?id=funding-rate-structure>`
        """
        await self.load_markets()
        market = self.market(symbol)
        paginate = False
        paginate, params = self.handle_option_and_params(params, 'fetchFundingRateHistory', 'paginate')
        maxEntriesPerRequest = 744  # seems exchange returns max 744 items per request
        eachItemDuration = '1h'
        if paginate:
            # fix for: https://github.com/ccxt/ccxt/issues/25040
            return await self.fetch_paginated_call_deterministic('fetchFundingRateHistory', symbol, since, limit, eachItemDuration, self.extend(params, {'isDeribitPaginationCall': True}), maxEntriesPerRequest)
        duration = self.parse_timeframe(eachItemDuration) * 1000
        time = self.milliseconds()
        month = 30 * 24 * 60 * 60 * 1000
        if since is None:
            since = time - month
        else:
            time = since + month
        request: dict = {
            'instrument_name': market['id'],
            'start_timestamp': since - 1,
        }
        until = self.safe_integer_2(params, 'until', 'end_timestamp')
        if until is not None:
            params = self.omit(params, ['until'])
            request['end_timestamp'] = until
        else:
            request['end_timestamp'] = time
        if 'isDeribitPaginationCall' in params:
            params = self.omit(params, 'isDeribitPaginationCall')
            maxUntil = self.sum(since, limit * duration)
            request['end_timestamp'] = min(request['end_timestamp'], maxUntil)
        response = await self.publicGetGetFundingRateHistory(self.extend(request, params))
        #
        #    {
        #        "jsonrpc": "2.0",
        #        "id": 7617,
        #        "result": [
        #          {
        #            "timestamp": 1569891600000,
        #            "index_price": 8222.87,
        #            "prev_index_price": 8305.72,
        #            "interest_8h": -0.00009234260068476106,
        #            "interest_1h": -4.739622041017375e-7
        #          }
        #        ]
        #    }
        #
        rates = []
        result = self.safe_value(response, 'result', [])
        for i in range(0, len(result)):
            fr = result[i]
            rate = self.parse_funding_rate(fr, market)
            rates.append(rate)
        return self.filter_by_symbol_since_limit(rates, symbol, since, limit)

    def parse_funding_rate(self, contract, market: Market = None) -> FundingRate:
        #
        #   {
        #       "jsonrpc":"2.0",
        #       "result":"0",
        #       "usIn":"1691161645596519",
        #       "usOut":"1691161645597149",
        #       "usDiff":"630",
        #       "testnet":false
        #   }
        # history
        #   {
        #     "timestamp": 1569891600000,
        #     "index_price": 8222.87,
        #     "prev_index_price": 8305.72,
        #     "interest_8h": -0.00009234260068476106,
        #     "interest_1h": -4.739622041017375e-7
        #   }
        #
        timestamp = self.safe_integer(contract, 'timestamp')
        datetime = self.iso8601(timestamp)
        result = self.safe_number_2(contract, 'result', 'interest_8h')
        return {
            'info': contract,
            'symbol': self.safe_symbol(None, market),
            'markPrice': None,
            'indexPrice': self.safe_number(contract, 'index_price'),
            'interestRate': None,
            'estimatedSettlePrice': None,
            'timestamp': timestamp,
            'datetime': datetime,
            'fundingRate': result,
            'fundingTimestamp': None,
            'fundingDatetime': None,
            'nextFundingRate': None,
            'nextFundingTimestamp': None,
            'nextFundingDatetime': None,
            'previousFundingRate': None,
            'previousFundingTimestamp': None,
            'previousFundingDatetime': None,
            'interval': '8h',
        }

    async def fetch_liquidations(self, symbol: str, since: Int = None, limit: Int = None, params={}):
        """
        retrieves the public liquidations of a trading pair

        https://docs.deribit.com/#public-get_last_settlements_by_currency

        :param str symbol: unified CCXT market symbol
        :param int [since]: the earliest time in ms to fetch liquidations for
        :param int [limit]: the maximum number of liquidation structures to retrieve
        :param dict [params]: exchange specific parameters for the deribit api endpoint
        :param boolean [params.paginate]: default False, when True will automatically paginate by calling self endpoint multiple times. See in the docs all the [availble parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-params)
        :returns dict: an array of `liquidation structures <https://docs.ccxt.com/#/?id=liquidation-structure>`
        """
        await self.load_markets()
        paginate = False
        paginate, params = self.handle_option_and_params(params, 'fetchLiquidations', 'paginate')
        if paginate:
            return await self.fetch_paginated_call_cursor('fetchLiquidations', symbol, since, limit, params, 'continuation', 'continuation', None)
        market = self.market(symbol)
        if market['spot']:
            raise NotSupported(self.id + ' fetchLiquidations() does not support ' + market['type'] + ' markets')
        request: dict = {
            'instrument_name': market['id'],
            'type': 'bankruptcy',
        }
        if since is not None:
            request['search_start_timestamp'] = since
        if limit is not None:
            request['count'] = limit
        response = await self.publicGetGetLastSettlementsByInstrument(self.extend(request, params))
        #
        #     {
        #         "jsonrpc": "2.0",
        #         "result": {
        #             "settlements": [
        #                 {
        #                     "type": "bankruptcy",
        #                     "timestamp": *************,
        #                     "funded": 10000.0,
        #                     "session_bankrupcy": 10000.0
        #                     "session_profit_loss": 112951.***********,
        #                     "session_tax": 0.15,
        #                     "session_tax_rate": 0.0015,
        #                     "socialized": 0.001,
        #                 },
        #             ],
        #             "continuation": "5dHzoGyD8Hs8KURoUhfgXgHpJTA5oyapoudSmNeAfEftqRbjNE6jNNUpo2oCu1khnZL9ao"
        #         },
        #         "usIn": ****************,
        #         "usOut": ****************,
        #         "usDiff": 843,
        #         "testnet": False
        #     }
        #
        result = self.safe_value(response, 'result', {})
        cursor = self.safe_string(result, 'continuation')
        settlements = self.safe_value(result, 'settlements', [])
        settlementsWithCursor = self.add_pagination_cursor_to_result(cursor, settlements)
        return self.parse_liquidations(settlementsWithCursor, market, since, limit)

    def add_pagination_cursor_to_result(self, cursor, data):
        if cursor is not None:
            dataLength = len(data)
            if dataLength > 0:
                first = data[0]
                last = data[dataLength - 1]
                first['continuation'] = cursor
                last['continuation'] = cursor
                data[0] = first
                data[dataLength - 1] = last
        return data

    async def fetch_my_liquidations(self, symbol: Str = None, since: Int = None, limit: Int = None, params={}):
        """
        retrieves the users liquidated positions

        https://docs.deribit.com/#private-get_settlement_history_by_instrument

        :param str symbol: unified CCXT market symbol
        :param int [since]: the earliest time in ms to fetch liquidations for
        :param int [limit]: the maximum number of liquidation structures to retrieve
        :param dict [params]: exchange specific parameters for the deribit api endpoint
        :returns dict: an array of `liquidation structures <https://docs.ccxt.com/#/?id=liquidation-structure>`
        """
        if symbol is None:
            raise ArgumentsRequired(self.id + ' fetchMyLiquidations() requires a symbol argument')
        await self.load_markets()
        market = self.market(symbol)
        if market['spot']:
            raise NotSupported(self.id + ' fetchMyLiquidations() does not support ' + market['type'] + ' markets')
        request: dict = {
            'instrument_name': market['id'],
            'type': 'bankruptcy',
        }
        if since is not None:
            request['search_start_timestamp'] = since
        if limit is not None:
            request['count'] = limit
        response = await self.privateGetGetSettlementHistoryByInstrument(self.extend(request, params))
        #
        #     {
        #         "jsonrpc": "2.0",
        #         "result": {
        #             "settlements": [
        #                 {
        #                     "type": "bankruptcy",
        #                     "timestamp": *************,
        #                     "funded": 10000.0,
        #                     "session_bankrupcy": 10000.0
        #                     "session_profit_loss": 112951.***********,
        #                     "session_tax": 0.15,
        #                     "session_tax_rate": 0.0015,
        #                     "socialized": 0.001,
        #                 },
        #             ],
        #             "continuation": "5dHzoGyD8Hs8KURoUhfgXgHpJTA5oyapoudSmNeAfEftqRbjNE6jNNUpo2oCu1khnZL9ao"
        #         },
        #         "usIn": ****************,
        #         "usOut": ****************,
        #         "usDiff": 843,
        #         "testnet": False
        #     }
        #
        result = self.safe_value(response, 'result', {})
        settlements = self.safe_list(result, 'settlements', [])
        return self.parse_liquidations(settlements, market, since, limit)

    def parse_liquidation(self, liquidation, market: Market = None):
        #
        #     {
        #         "type": "bankruptcy",
        #         "timestamp": *************,
        #         "funded": 1,
        #         "session_bankrupcy": 0.001,
        #         "session_profit_loss": 0.001,
        #         "session_tax": 0.0015,
        #         "session_tax_rate": 0.0015,
        #         "socialized": 0.001,
        #     }
        #
        timestamp = self.safe_integer(liquidation, 'timestamp')
        return self.safe_liquidation({
            'info': liquidation,
            'symbol': self.safe_symbol(None, market),
            'contracts': None,
            'contractSize': self.safe_number(market, 'contractSize'),
            'price': None,
            'baseValue': self.safe_number(liquidation, 'session_bankrupcy'),
            'quoteValue': None,
            'timestamp': timestamp,
            'datetime': self.iso8601(timestamp),
        })

    async def fetch_greeks(self, symbol: str, params={}) -> Greeks:
        """
        fetches an option contracts greeks, financial metrics used to measure the factors that affect the price of an options contract

        https://docs.deribit.com/#public-ticker

        :param str symbol: unified symbol of the market to fetch greeks for
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `greeks structure <https://docs.ccxt.com/#/?id=greeks-structure>`
        """
        await self.load_markets()
        market = self.market(symbol)
        request: dict = {
            'instrument_name': market['id'],
        }
        response = await self.publicGetTicker(self.extend(request, params))
        #
        #     {
        #         "jsonrpc": "2.0",
        #         "result": {
        #             "estimated_delivery_price": 36552.72,
        #             "best_bid_amount": 0.2,
        #             "best_ask_amount": 9.1,
        #             "interest_rate": 0.0,
        #             "best_bid_price": 0.214,
        #             "best_ask_price": 0.219,
        #             "open_interest": 368.8,
        #             "settlement_price": 0.22103022,
        #             "last_price": 0.215,
        #             "bid_iv": 60.51,
        #             "ask_iv": 61.88,
        #             "mark_iv": 61.27,
        #             "underlying_index": "BTC-27SEP24",
        #             "underlying_price": 38992.71,
        #             "min_price": 0.1515,
        #             "max_price": 0.326,
        #             "mark_price": 0.2168,
        #             "instrument_name": "BTC-27SEP24-40000-C",
        #             "index_price": 36552.72,
        #             "greeks": {
        #                 "rho": 130.63998,
        #                 "theta": -13.48784,
        #                 "vega": 141.90146,
        #                 "gamma": 0.00002,
        #                 "delta": 0.59621
        #             },
        #             "stats": {
        #                 "volume_usd": 100453.9,
        #                 "volume": 12.0,
        #                 "price_change": -2.2727,
        #                 "low": 0.2065,
        #                 "high": 0.238
        #             },
        #             "state": "open",
        #             "timestamp": 1699578548021
        #         },
        #         "usIn": 1699578548308414,
        #         "usOut": 1699578548308606,
        #         "usDiff": 192,
        #         "testnet": False
        #     }
        #
        result = self.safe_value(response, 'result', {})
        return self.parse_greeks(result, market)

    def parse_greeks(self, greeks: dict, market: Market = None) -> Greeks:
        #
        #     {
        #         "estimated_delivery_price": 36552.72,
        #         "best_bid_amount": 0.2,
        #         "best_ask_amount": 9.1,
        #         "interest_rate": 0.0,
        #         "best_bid_price": 0.214,
        #         "best_ask_price": 0.219,
        #         "open_interest": 368.8,
        #         "settlement_price": 0.22103022,
        #         "last_price": 0.215,
        #         "bid_iv": 60.51,
        #         "ask_iv": 61.88,
        #         "mark_iv": 61.27,
        #         "underlying_index": "BTC-27SEP24",
        #         "underlying_price": 38992.71,
        #         "min_price": 0.1515,
        #         "max_price": 0.326,
        #         "mark_price": 0.2168,
        #         "instrument_name": "BTC-27SEP24-40000-C",
        #         "index_price": 36552.72,
        #         "greeks": {
        #             "rho": 130.63998,
        #             "theta": -13.48784,
        #             "vega": 141.90146,
        #             "gamma": 0.00002,
        #             "delta": 0.59621
        #         },
        #         "stats": {
        #             "volume_usd": 100453.9,
        #             "volume": 12.0,
        #             "price_change": -2.2727,
        #             "low": 0.2065,
        #             "high": 0.238
        #         },
        #         "state": "open",
        #         "timestamp": 1699578548021
        #     }
        #
        timestamp = self.safe_integer(greeks, 'timestamp')
        marketId = self.safe_string(greeks, 'instrument_name')
        symbol = self.safe_symbol(marketId, market)
        stats = self.safe_value(greeks, 'greeks', {})
        return {
            'symbol': symbol,
            'timestamp': timestamp,
            'datetime': self.iso8601(timestamp),
            'delta': self.safe_number(stats, 'delta'),
            'gamma': self.safe_number(stats, 'gamma'),
            'theta': self.safe_number(stats, 'theta'),
            'vega': self.safe_number(stats, 'vega'),
            'rho': self.safe_number(stats, 'rho'),
            'bidSize': self.safe_number(greeks, 'best_bid_amount'),
            'askSize': self.safe_number(greeks, 'best_ask_amount'),
            'bidImpliedVolatility': self.safe_number(greeks, 'bid_iv'),
            'askImpliedVolatility': self.safe_number(greeks, 'ask_iv'),
            'markImpliedVolatility': self.safe_number(greeks, 'mark_iv'),
            'bidPrice': self.safe_number(greeks, 'best_bid_price'),
            'askPrice': self.safe_number(greeks, 'best_ask_price'),
            'markPrice': self.safe_number(greeks, 'mark_price'),
            'lastPrice': self.safe_number(greeks, 'last_price'),
            'underlyingPrice': self.safe_number(greeks, 'underlying_price'),
            'info': greeks,
        }

    async def fetch_option(self, symbol: str, params={}) -> Option:
        """
        fetches option data that is commonly found in an option chain

        https://docs.deribit.com/#public-get_book_summary_by_instrument

        :param str symbol: unified market symbol
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: an `option chain structure <https://docs.ccxt.com/#/?id=option-chain-structure>`
        """
        await self.load_markets()
        market = self.market(symbol)
        request: dict = {
            'instrument_name': market['id'],
        }
        response = await self.publicGetGetBookSummaryByInstrument(self.extend(request, params))
        #
        #     {
        #         "jsonrpc": "2.0",
        #         "result": [
        #             {
        #                 "mid_price": 0.04025,
        #                 "volume_usd": 11045.12,
        #                 "quote_currency": "BTC",
        #                 "estimated_delivery_price": 65444.72,
        #                 "creation_timestamp": 1711100949273,
        #                 "base_currency": "BTC",
        #                 "underlying_index": "BTC-27DEC24",
        #                 "underlying_price": 73742.14,
        #                 "volume": 4.0,
        #                 "interest_rate": 0.0,
        #                 "price_change": -6.9767,
        #                 "open_interest": 274.2,
        #                 "ask_price": 0.042,
        #                 "bid_price": 0.0385,
        #                 "instrument_name": "BTC-27DEC24-240000-C",
        #                 "mark_price": 0.04007735,
        #                 "last": 0.04,
        #                 "low": 0.04,
        #                 "high": 0.043
        #             }
        #         ],
        #         "usIn": 1711100949273223,
        #         "usOut": 1711100949273580,
        #         "usDiff": 357,
        #         "testnet": False
        #     }
        #
        result = self.safe_list(response, 'result', [])
        chain = self.safe_dict(result, 0, {})
        return self.parse_option(chain, None, market)

    async def fetch_option_chain(self, code: str, params={}) -> OptionChain:
        """
        fetches data for an underlying asset that is commonly found in an option chain

        https://docs.deribit.com/#public-get_book_summary_by_currency

        :param str code: base currency to fetch an option chain for
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a list of `option chain structures <https://docs.ccxt.com/#/?id=option-chain-structure>`
        """
        await self.load_markets()
        currency = self.currency(code)
        request: dict = {
            'currency': currency['id'],
            'kind': 'option',
        }
        response = await self.publicGetGetBookSummaryByCurrency(self.extend(request, params))
        #
        #     {
        #         "jsonrpc": "2.0",
        #         "result": [
        #             {
        #                 "mid_price": 0.4075,
        #                 "volume_usd": 2836.83,
        #                 "quote_currency": "BTC",
        #                 "estimated_delivery_price": 65479.26,
        #                 "creation_timestamp": 1711101594477,
        #                 "base_currency": "BTC",
        #                 "underlying_index": "BTC-28JUN24",
        #                 "underlying_price": 68827.27,
        #                 "volume": 0.1,
        #                 "interest_rate": 0.0,
        #                 "price_change": 0.0,
        #                 "open_interest": 364.1,
        #                 "ask_price": 0.411,
        #                 "bid_price": 0.404,
        #                 "instrument_name": "BTC-28JUN24-42000-C",
        #                 "mark_price": 0.40752052,
        #                 "last": 0.423,
        #                 "low": 0.423,
        #                 "high": 0.423
        #             }
        #         ],
        #         "usIn": 1711101594456388,
        #         "usOut": 1711101594484065,
        #         "usDiff": 27677,
        #         "testnet": False
        #     }
        #
        result = self.safe_list(response, 'result', [])
        return self.parse_option_chain(result, 'base_currency', 'instrument_name')

    def parse_option(self, chain: dict, currency: Currency = None, market: Market = None) -> Option:
        #
        #     {
        #         "mid_price": 0.04025,
        #         "volume_usd": 11045.12,
        #         "quote_currency": "BTC",
        #         "estimated_delivery_price": 65444.72,
        #         "creation_timestamp": 1711100949273,
        #         "base_currency": "BTC",
        #         "underlying_index": "BTC-27DEC24",
        #         "underlying_price": 73742.14,
        #         "volume": 4.0,
        #         "interest_rate": 0.0,
        #         "price_change": -6.9767,
        #         "open_interest": 274.2,
        #         "ask_price": 0.042,
        #         "bid_price": 0.0385,
        #         "instrument_name": "BTC-27DEC24-240000-C",
        #         "mark_price": 0.04007735,
        #         "last": 0.04,
        #         "low": 0.04,
        #         "high": 0.043
        #     }
        #
        marketId = self.safe_string(chain, 'instrument_name')
        market = self.safe_market(marketId, market)
        currencyId = self.safe_string(chain, 'base_currency')
        code = self.safe_currency_code(currencyId, currency)
        timestamp = self.safe_integer(chain, 'timestamp')
        return {
            'info': chain,
            'currency': code,
            'symbol': market['symbol'],
            'timestamp': timestamp,
            'datetime': self.iso8601(timestamp),
            'impliedVolatility': None,
            'openInterest': self.safe_number(chain, 'open_interest'),
            'bidPrice': self.safe_number(chain, 'bid_price'),
            'askPrice': self.safe_number(chain, 'ask_price'),
            'midPrice': self.safe_number(chain, 'mid_price'),
            'markPrice': self.safe_number(chain, 'mark_price'),
            'lastPrice': self.safe_number(chain, 'last'),
            'underlyingPrice': self.safe_number(chain, 'underlying_price'),
            'change': None,
            'percentage': self.safe_number(chain, 'price_change'),
            'baseVolume': self.safe_number(chain, 'volume'),
            'quoteVolume': self.safe_number(chain, 'volume_usd'),
        }

    def nonce(self):
        return self.milliseconds()

    def sign(self, path, api='public', method='GET', params={}, headers=None, body=None):
        request = '/' + 'api/' + self.version + '/' + api + '/' + path
        if api == 'public':
            if params:
                request += '?' + self.urlencode(params)
        if api == 'private':
            self.check_required_credentials()
            nonce = str(self.nonce())
            timestamp = str(self.milliseconds())
            requestBody = ''
            if params:
                request += '?' + self.urlencode(params)
            requestData = method + "\n" + request + "\n" + requestBody + "\n"  # eslint-disable-line quotes
            auth = timestamp + "\n" + nonce + "\n" + requestData  # eslint-disable-line quotes
            signature = self.hmac(self.encode(auth), self.encode(self.secret), hashlib.sha256)
            headers = {
                'Authorization': 'deri-hmac-sha256 id=' + self.apiKey + ',ts=' + timestamp + ',sig=' + signature + ',' + 'nonce=' + nonce,
            }
        url = self.urls['api']['rest'] + request
        return {'url': url, 'method': method, 'body': body, 'headers': headers}

    def handle_errors(self, httpCode: int, reason: str, url: str, method: str, headers: dict, body: str, response, requestHeaders, requestBody):
        if not response:
            return None  # fallback to default error handler
        #
        #     {
        #         "jsonrpc": "2.0",
        #         "error": {
        #             "message": "Invalid params",
        #             "data": {reason: "invalid currency", param: "currency"},
        #             "code": -32602
        #         },
        #         "testnet": False,
        #         "usIn": 1583763842150374,
        #         "usOut": 1583763842150410,
        #         "usDiff": 36
        #     }
        #
        error = self.safe_value(response, 'error')
        if error is not None:
            errorCode = self.safe_string(error, 'code')
            feedback = self.id + ' ' + body
            self.throw_exactly_matched_exception(self.exceptions, errorCode, feedback)
            raise ExchangeError(feedback)  # unknown message
        return None
