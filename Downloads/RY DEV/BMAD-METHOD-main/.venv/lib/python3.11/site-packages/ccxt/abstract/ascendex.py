from ccxt.base.types import Entry


class ImplicitAPI:
    v1_public_get_assets = v1PublicGetAssets = Entry('assets', ['v1', 'public'], 'GET', {'cost': 1})
    v1_public_get_products = v1PublicGetProducts = Entry('products', ['v1', 'public'], 'GET', {'cost': 1})
    v1_public_get_ticker = v1PublicGetTicker = Entry('ticker', ['v1', 'public'], 'GET', {'cost': 1})
    v1_public_get_barhist_info = v1PublicGetBarhistInfo = Entry('barhist/info', ['v1', 'public'], 'GET', {'cost': 1})
    v1_public_get_barhist = v1PublicGetBarhist = Entry('barhist', ['v1', 'public'], 'GET', {'cost': 1})
    v1_public_get_depth = v1PublicGetDepth = Entry('depth', ['v1', 'public'], 'GET', {'cost': 1})
    v1_public_get_trades = v1PublicGetTrades = Entry('trades', ['v1', 'public'], 'GET', {'cost': 1})
    v1_public_get_cash_assets = v1PublicGetCashAssets = Entry('cash/assets', ['v1', 'public'], 'GET', {'cost': 1})
    v1_public_get_cash_products = v1PublicGetCashProducts = Entry('cash/products', ['v1', 'public'], 'GET', {'cost': 1})
    v1_public_get_margin_assets = v1PublicGetMarginAssets = Entry('margin/assets', ['v1', 'public'], 'GET', {'cost': 1})
    v1_public_get_margin_products = v1PublicGetMarginProducts = Entry('margin/products', ['v1', 'public'], 'GET', {'cost': 1})
    v1_public_get_futures_collateral = v1PublicGetFuturesCollateral = Entry('futures/collateral', ['v1', 'public'], 'GET', {'cost': 1})
    v1_public_get_futures_contracts = v1PublicGetFuturesContracts = Entry('futures/contracts', ['v1', 'public'], 'GET', {'cost': 1})
    v1_public_get_futures_ref_px = v1PublicGetFuturesRefPx = Entry('futures/ref-px', ['v1', 'public'], 'GET', {'cost': 1})
    v1_public_get_futures_market_data = v1PublicGetFuturesMarketData = Entry('futures/market-data', ['v1', 'public'], 'GET', {'cost': 1})
    v1_public_get_futures_funding_rates = v1PublicGetFuturesFundingRates = Entry('futures/funding-rates', ['v1', 'public'], 'GET', {'cost': 1})
    v1_public_get_risk_limit_info = v1PublicGetRiskLimitInfo = Entry('risk-limit-info', ['v1', 'public'], 'GET', {'cost': 1})
    v1_public_get_exchange_info = v1PublicGetExchangeInfo = Entry('exchange-info', ['v1', 'public'], 'GET', {'cost': 1})
    v1_private_get_info = v1PrivateGetInfo = Entry('info', ['v1', 'private'], 'GET', {'cost': 1})
    v1_private_get_wallet_transactions = v1PrivateGetWalletTransactions = Entry('wallet/transactions', ['v1', 'private'], 'GET', {'cost': 1})
    v1_private_get_wallet_deposit_address = v1PrivateGetWalletDepositAddress = Entry('wallet/deposit/address', ['v1', 'private'], 'GET', {'cost': 1})
    v1_private_get_data_balance_snapshot = v1PrivateGetDataBalanceSnapshot = Entry('data/balance/snapshot', ['v1', 'private'], 'GET', {'cost': 1})
    v1_private_get_data_balance_history = v1PrivateGetDataBalanceHistory = Entry('data/balance/history', ['v1', 'private'], 'GET', {'cost': 1})
    v1_private_accountcategory_get_balance = v1PrivateAccountCategoryGetBalance = Entry('balance', ['v1', 'private', 'accountCategory'], 'GET', {'cost': 1})
    v1_private_accountcategory_get_order_open = v1PrivateAccountCategoryGetOrderOpen = Entry('order/open', ['v1', 'private', 'accountCategory'], 'GET', {'cost': 1})
    v1_private_accountcategory_get_order_status = v1PrivateAccountCategoryGetOrderStatus = Entry('order/status', ['v1', 'private', 'accountCategory'], 'GET', {'cost': 1})
    v1_private_accountcategory_get_order_hist_current = v1PrivateAccountCategoryGetOrderHistCurrent = Entry('order/hist/current', ['v1', 'private', 'accountCategory'], 'GET', {'cost': 1})
    v1_private_accountcategory_get_risk = v1PrivateAccountCategoryGetRisk = Entry('risk', ['v1', 'private', 'accountCategory'], 'GET', {'cost': 1})
    v1_private_accountcategory_post_order = v1PrivateAccountCategoryPostOrder = Entry('order', ['v1', 'private', 'accountCategory'], 'POST', {'cost': 1})
    v1_private_accountcategory_post_order_batch = v1PrivateAccountCategoryPostOrderBatch = Entry('order/batch', ['v1', 'private', 'accountCategory'], 'POST', {'cost': 1})
    v1_private_accountcategory_delete_order = v1PrivateAccountCategoryDeleteOrder = Entry('order', ['v1', 'private', 'accountCategory'], 'DELETE', {'cost': 1})
    v1_private_accountcategory_delete_order_all = v1PrivateAccountCategoryDeleteOrderAll = Entry('order/all', ['v1', 'private', 'accountCategory'], 'DELETE', {'cost': 1})
    v1_private_accountcategory_delete_order_batch = v1PrivateAccountCategoryDeleteOrderBatch = Entry('order/batch', ['v1', 'private', 'accountCategory'], 'DELETE', {'cost': 1})
    v1_private_accountgroup_get_cash_balance = v1PrivateAccountGroupGetCashBalance = Entry('cash/balance', ['v1', 'private', 'accountGroup'], 'GET', {'cost': 1})
    v1_private_accountgroup_get_margin_balance = v1PrivateAccountGroupGetMarginBalance = Entry('margin/balance', ['v1', 'private', 'accountGroup'], 'GET', {'cost': 1})
    v1_private_accountgroup_get_margin_risk = v1PrivateAccountGroupGetMarginRisk = Entry('margin/risk', ['v1', 'private', 'accountGroup'], 'GET', {'cost': 1})
    v1_private_accountgroup_get_futures_collateral_balance = v1PrivateAccountGroupGetFuturesCollateralBalance = Entry('futures/collateral-balance', ['v1', 'private', 'accountGroup'], 'GET', {'cost': 1})
    v1_private_accountgroup_get_futures_position = v1PrivateAccountGroupGetFuturesPosition = Entry('futures/position', ['v1', 'private', 'accountGroup'], 'GET', {'cost': 1})
    v1_private_accountgroup_get_futures_risk = v1PrivateAccountGroupGetFuturesRisk = Entry('futures/risk', ['v1', 'private', 'accountGroup'], 'GET', {'cost': 1})
    v1_private_accountgroup_get_futures_funding_payments = v1PrivateAccountGroupGetFuturesFundingPayments = Entry('futures/funding-payments', ['v1', 'private', 'accountGroup'], 'GET', {'cost': 1})
    v1_private_accountgroup_get_order_hist = v1PrivateAccountGroupGetOrderHist = Entry('order/hist', ['v1', 'private', 'accountGroup'], 'GET', {'cost': 1})
    v1_private_accountgroup_get_spot_fee = v1PrivateAccountGroupGetSpotFee = Entry('spot/fee', ['v1', 'private', 'accountGroup'], 'GET', {'cost': 1})
    v1_private_accountgroup_post_transfer = v1PrivateAccountGroupPostTransfer = Entry('transfer', ['v1', 'private', 'accountGroup'], 'POST', {'cost': 1})
    v1_private_accountgroup_post_futures_transfer_deposit = v1PrivateAccountGroupPostFuturesTransferDeposit = Entry('futures/transfer/deposit', ['v1', 'private', 'accountGroup'], 'POST', {'cost': 1})
    v1_private_accountgroup_post_futures_transfer_withdraw = v1PrivateAccountGroupPostFuturesTransferWithdraw = Entry('futures/transfer/withdraw', ['v1', 'private', 'accountGroup'], 'POST', {'cost': 1})
    v2_public_get_assets = v2PublicGetAssets = Entry('assets', ['v2', 'public'], 'GET', {'cost': 1})
    v2_public_get_futures_contract = v2PublicGetFuturesContract = Entry('futures/contract', ['v2', 'public'], 'GET', {'cost': 1})
    v2_public_get_futures_collateral = v2PublicGetFuturesCollateral = Entry('futures/collateral', ['v2', 'public'], 'GET', {'cost': 1})
    v2_public_get_futures_pricing_data = v2PublicGetFuturesPricingData = Entry('futures/pricing-data', ['v2', 'public'], 'GET', {'cost': 1})
    v2_public_get_futures_ticker = v2PublicGetFuturesTicker = Entry('futures/ticker', ['v2', 'public'], 'GET', {'cost': 1})
    v2_public_get_risk_limit_info = v2PublicGetRiskLimitInfo = Entry('risk-limit-info', ['v2', 'public'], 'GET', {'cost': 1})
    v2_private_data_get_order_hist = v2PrivateDataGetOrderHist = Entry('order/hist', ['v2', 'private', 'data'], 'GET', {'cost': 1})
    v2_private_get_account_info = v2PrivateGetAccountInfo = Entry('account/info', ['v2', 'private'], 'GET', {'cost': 1})
    v2_private_accountgroup_get_order_hist = v2PrivateAccountGroupGetOrderHist = Entry('order/hist', ['v2', 'private', 'accountGroup'], 'GET', {'cost': 1})
    v2_private_accountgroup_get_futures_position = v2PrivateAccountGroupGetFuturesPosition = Entry('futures/position', ['v2', 'private', 'accountGroup'], 'GET', {'cost': 1})
    v2_private_accountgroup_get_futures_free_margin = v2PrivateAccountGroupGetFuturesFreeMargin = Entry('futures/free-margin', ['v2', 'private', 'accountGroup'], 'GET', {'cost': 1})
    v2_private_accountgroup_get_futures_order_hist_current = v2PrivateAccountGroupGetFuturesOrderHistCurrent = Entry('futures/order/hist/current', ['v2', 'private', 'accountGroup'], 'GET', {'cost': 1})
    v2_private_accountgroup_get_futures_funding_payments = v2PrivateAccountGroupGetFuturesFundingPayments = Entry('futures/funding-payments', ['v2', 'private', 'accountGroup'], 'GET', {'cost': 1})
    v2_private_accountgroup_get_futures_order_open = v2PrivateAccountGroupGetFuturesOrderOpen = Entry('futures/order/open', ['v2', 'private', 'accountGroup'], 'GET', {'cost': 1})
    v2_private_accountgroup_get_futures_order_status = v2PrivateAccountGroupGetFuturesOrderStatus = Entry('futures/order/status', ['v2', 'private', 'accountGroup'], 'GET', {'cost': 1})
    v2_private_accountgroup_post_futures_isolated_position_margin = v2PrivateAccountGroupPostFuturesIsolatedPositionMargin = Entry('futures/isolated-position-margin', ['v2', 'private', 'accountGroup'], 'POST', {'cost': 1})
    v2_private_accountgroup_post_futures_margin_type = v2PrivateAccountGroupPostFuturesMarginType = Entry('futures/margin-type', ['v2', 'private', 'accountGroup'], 'POST', {'cost': 1})
    v2_private_accountgroup_post_futures_leverage = v2PrivateAccountGroupPostFuturesLeverage = Entry('futures/leverage', ['v2', 'private', 'accountGroup'], 'POST', {'cost': 1})
    v2_private_accountgroup_post_futures_transfer_deposit = v2PrivateAccountGroupPostFuturesTransferDeposit = Entry('futures/transfer/deposit', ['v2', 'private', 'accountGroup'], 'POST', {'cost': 1})
    v2_private_accountgroup_post_futures_transfer_withdraw = v2PrivateAccountGroupPostFuturesTransferWithdraw = Entry('futures/transfer/withdraw', ['v2', 'private', 'accountGroup'], 'POST', {'cost': 1})
    v2_private_accountgroup_post_futures_order = v2PrivateAccountGroupPostFuturesOrder = Entry('futures/order', ['v2', 'private', 'accountGroup'], 'POST', {'cost': 1})
    v2_private_accountgroup_post_futures_order_batch = v2PrivateAccountGroupPostFuturesOrderBatch = Entry('futures/order/batch', ['v2', 'private', 'accountGroup'], 'POST', {'cost': 1})
    v2_private_accountgroup_post_futures_order_open = v2PrivateAccountGroupPostFuturesOrderOpen = Entry('futures/order/open', ['v2', 'private', 'accountGroup'], 'POST', {'cost': 1})
    v2_private_accountgroup_post_subuser_subuser_transfer = v2PrivateAccountGroupPostSubuserSubuserTransfer = Entry('subuser/subuser-transfer', ['v2', 'private', 'accountGroup'], 'POST', {'cost': 1})
    v2_private_accountgroup_post_subuser_subuser_transfer_hist = v2PrivateAccountGroupPostSubuserSubuserTransferHist = Entry('subuser/subuser-transfer-hist', ['v2', 'private', 'accountGroup'], 'POST', {'cost': 1})
    v2_private_accountgroup_delete_futures_order = v2PrivateAccountGroupDeleteFuturesOrder = Entry('futures/order', ['v2', 'private', 'accountGroup'], 'DELETE', {'cost': 1})
    v2_private_accountgroup_delete_futures_order_batch = v2PrivateAccountGroupDeleteFuturesOrderBatch = Entry('futures/order/batch', ['v2', 'private', 'accountGroup'], 'DELETE', {'cost': 1})
    v2_private_accountgroup_delete_futures_order_all = v2PrivateAccountGroupDeleteFuturesOrderAll = Entry('futures/order/all', ['v2', 'private', 'accountGroup'], 'DELETE', {'cost': 1})
