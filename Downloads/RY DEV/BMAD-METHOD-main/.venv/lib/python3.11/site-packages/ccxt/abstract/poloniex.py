from ccxt.base.types import Entry


class ImplicitAPI:
    public_get_markets = publicGetMarkets = Entry('markets', 'public', 'GET', {'cost': 20})
    public_get_markets_symbol = publicGetMarketsSymbol = Entry('markets/{symbol}', 'public', 'GET', {'cost': 1})
    public_get_currencies = publicGetCurrencies = Entry('currencies', 'public', 'GET', {'cost': 20})
    public_get_currencies_currency = publicGetCurrenciesCurrency = Entry('currencies/{currency}', 'public', 'GET', {'cost': 20})
    public_get_v2_currencies = publicGetV2Currencies = Entry('v2/currencies', 'public', 'GET', {'cost': 20})
    public_get_v2_currencies_currency = publicGetV2CurrenciesCurrency = Entry('v2/currencies/{currency}', 'public', 'GET', {'cost': 20})
    public_get_timestamp = publicGetTimestamp = Entry('timestamp', 'public', 'GET', {'cost': 1})
    public_get_markets_price = publicGetMarketsPrice = Entry('markets/price', 'public', 'GET', {'cost': 1})
    public_get_markets_symbol_price = publicGetMarketsSymbolPrice = Entry('markets/{symbol}/price', 'public', 'GET', {'cost': 1})
    public_get_markets_markprice = publicGetMarketsMarkPrice = Entry('markets/markPrice', 'public', 'GET', {'cost': 1})
    public_get_markets_symbol_markprice = publicGetMarketsSymbolMarkPrice = Entry('markets/{symbol}/markPrice', 'public', 'GET', {'cost': 1})
    public_get_markets_symbol_markpricecomponents = publicGetMarketsSymbolMarkPriceComponents = Entry('markets/{symbol}/markPriceComponents', 'public', 'GET', {'cost': 1})
    public_get_markets_symbol_orderbook = publicGetMarketsSymbolOrderBook = Entry('markets/{symbol}/orderBook', 'public', 'GET', {'cost': 1})
    public_get_markets_symbol_candles = publicGetMarketsSymbolCandles = Entry('markets/{symbol}/candles', 'public', 'GET', {'cost': 1})
    public_get_markets_symbol_trades = publicGetMarketsSymbolTrades = Entry('markets/{symbol}/trades', 'public', 'GET', {'cost': 20})
    public_get_markets_ticker24h = publicGetMarketsTicker24h = Entry('markets/ticker24h', 'public', 'GET', {'cost': 20})
    public_get_markets_symbol_ticker24h = publicGetMarketsSymbolTicker24h = Entry('markets/{symbol}/ticker24h', 'public', 'GET', {'cost': 20})
    public_get_markets_collateralinfo = publicGetMarketsCollateralInfo = Entry('markets/collateralInfo', 'public', 'GET', {'cost': 1})
    public_get_markets_currency_collateralinfo = publicGetMarketsCurrencyCollateralInfo = Entry('markets/{currency}/collateralInfo', 'public', 'GET', {'cost': 1})
    public_get_markets_borrowratesinfo = publicGetMarketsBorrowRatesInfo = Entry('markets/borrowRatesInfo', 'public', 'GET', {'cost': 1})
    private_get_accounts = privateGetAccounts = Entry('accounts', 'private', 'GET', {'cost': 4})
    private_get_accounts_balances = privateGetAccountsBalances = Entry('accounts/balances', 'private', 'GET', {'cost': 4})
    private_get_accounts_id_balances = privateGetAccountsIdBalances = Entry('accounts/{id}/balances', 'private', 'GET', {'cost': 4})
    private_get_accounts_activity = privateGetAccountsActivity = Entry('accounts/activity', 'private', 'GET', {'cost': 20})
    private_get_accounts_transfer = privateGetAccountsTransfer = Entry('accounts/transfer', 'private', 'GET', {'cost': 20})
    private_get_accounts_transfer_id = privateGetAccountsTransferId = Entry('accounts/transfer/{id}', 'private', 'GET', {'cost': 4})
    private_get_feeinfo = privateGetFeeinfo = Entry('feeinfo', 'private', 'GET', {'cost': 20})
    private_get_accounts_interest_history = privateGetAccountsInterestHistory = Entry('accounts/interest/history', 'private', 'GET', {'cost': 1})
    private_get_subaccounts = privateGetSubaccounts = Entry('subaccounts', 'private', 'GET', {'cost': 4})
    private_get_subaccounts_balances = privateGetSubaccountsBalances = Entry('subaccounts/balances', 'private', 'GET', {'cost': 20})
    private_get_subaccounts_id_balances = privateGetSubaccountsIdBalances = Entry('subaccounts/{id}/balances', 'private', 'GET', {'cost': 4})
    private_get_subaccounts_transfer = privateGetSubaccountsTransfer = Entry('subaccounts/transfer', 'private', 'GET', {'cost': 20})
    private_get_subaccounts_transfer_id = privateGetSubaccountsTransferId = Entry('subaccounts/transfer/{id}', 'private', 'GET', {'cost': 4})
    private_get_wallets_addresses = privateGetWalletsAddresses = Entry('wallets/addresses', 'private', 'GET', {'cost': 20})
    private_get_wallets_addresses_currency = privateGetWalletsAddressesCurrency = Entry('wallets/addresses/{currency}', 'private', 'GET', {'cost': 20})
    private_get_wallets_activity = privateGetWalletsActivity = Entry('wallets/activity', 'private', 'GET', {'cost': 20})
    private_get_margin_accountmargin = privateGetMarginAccountMargin = Entry('margin/accountMargin', 'private', 'GET', {'cost': 4})
    private_get_margin_borrowstatus = privateGetMarginBorrowStatus = Entry('margin/borrowStatus', 'private', 'GET', {'cost': 4})
    private_get_margin_maxsize = privateGetMarginMaxSize = Entry('margin/maxSize', 'private', 'GET', {'cost': 4})
    private_get_orders = privateGetOrders = Entry('orders', 'private', 'GET', {'cost': 20})
    private_get_orders_id = privateGetOrdersId = Entry('orders/{id}', 'private', 'GET', {'cost': 4})
    private_get_orders_killswitchstatus = privateGetOrdersKillSwitchStatus = Entry('orders/killSwitchStatus', 'private', 'GET', {'cost': 4})
    private_get_smartorders = privateGetSmartorders = Entry('smartorders', 'private', 'GET', {'cost': 20})
    private_get_smartorders_id = privateGetSmartordersId = Entry('smartorders/{id}', 'private', 'GET', {'cost': 4})
    private_get_orders_history = privateGetOrdersHistory = Entry('orders/history', 'private', 'GET', {'cost': 20})
    private_get_smartorders_history = privateGetSmartordersHistory = Entry('smartorders/history', 'private', 'GET', {'cost': 20})
    private_get_trades = privateGetTrades = Entry('trades', 'private', 'GET', {'cost': 20})
    private_get_orders_id_trades = privateGetOrdersIdTrades = Entry('orders/{id}/trades', 'private', 'GET', {'cost': 4})
    private_post_accounts_transfer = privatePostAccountsTransfer = Entry('accounts/transfer', 'private', 'POST', {'cost': 4})
    private_post_subaccounts_transfer = privatePostSubaccountsTransfer = Entry('subaccounts/transfer', 'private', 'POST', {'cost': 20})
    private_post_wallets_address = privatePostWalletsAddress = Entry('wallets/address', 'private', 'POST', {'cost': 20})
    private_post_wallets_withdraw = privatePostWalletsWithdraw = Entry('wallets/withdraw', 'private', 'POST', {'cost': 20})
    private_post_v2_wallets_withdraw = privatePostV2WalletsWithdraw = Entry('v2/wallets/withdraw', 'private', 'POST', {'cost': 20})
    private_post_orders = privatePostOrders = Entry('orders', 'private', 'POST', {'cost': 4})
    private_post_orders_batch = privatePostOrdersBatch = Entry('orders/batch', 'private', 'POST', {'cost': 20})
    private_post_orders_killswitch = privatePostOrdersKillSwitch = Entry('orders/killSwitch', 'private', 'POST', {'cost': 4})
    private_post_smartorders = privatePostSmartorders = Entry('smartorders', 'private', 'POST', {'cost': 4})
    private_delete_orders_id = privateDeleteOrdersId = Entry('orders/{id}', 'private', 'DELETE', {'cost': 4})
    private_delete_orders_cancelbyids = privateDeleteOrdersCancelByIds = Entry('orders/cancelByIds', 'private', 'DELETE', {'cost': 20})
    private_delete_orders = privateDeleteOrders = Entry('orders', 'private', 'DELETE', {'cost': 20})
    private_delete_smartorders_id = privateDeleteSmartordersId = Entry('smartorders/{id}', 'private', 'DELETE', {'cost': 4})
    private_delete_smartorders_cancelbyids = privateDeleteSmartordersCancelByIds = Entry('smartorders/cancelByIds', 'private', 'DELETE', {'cost': 20})
    private_delete_smartorders = privateDeleteSmartorders = Entry('smartorders', 'private', 'DELETE', {'cost': 20})
    private_put_orders_id = privatePutOrdersId = Entry('orders/{id}', 'private', 'PUT', {'cost': 20})
    private_put_smartorders_id = privatePutSmartordersId = Entry('smartorders/{id}', 'private', 'PUT', {'cost': 20})
    swappublic_get_v3_market_allinstruments = swapPublicGetV3MarketAllInstruments = Entry('v3/market/allInstruments', 'swapPublic', 'GET', {'cost': 0.****************})
    swappublic_get_v3_market_instruments = swapPublicGetV3MarketInstruments = Entry('v3/market/instruments', 'swapPublic', 'GET', {'cost': 0.****************})
    swappublic_get_v3_market_orderbook = swapPublicGetV3MarketOrderBook = Entry('v3/market/orderBook', 'swapPublic', 'GET', {'cost': 0.****************})
    swappublic_get_v3_market_candles = swapPublicGetV3MarketCandles = Entry('v3/market/candles', 'swapPublic', 'GET', {'cost': 10})
    swappublic_get_v3_market_indexpricecandlesticks = swapPublicGetV3MarketIndexPriceCandlesticks = Entry('v3/market/indexPriceCandlesticks', 'swapPublic', 'GET', {'cost': 10})
    swappublic_get_v3_market_premiumindexcandlesticks = swapPublicGetV3MarketPremiumIndexCandlesticks = Entry('v3/market/premiumIndexCandlesticks', 'swapPublic', 'GET', {'cost': 10})
    swappublic_get_v3_market_markpricecandlesticks = swapPublicGetV3MarketMarkPriceCandlesticks = Entry('v3/market/markPriceCandlesticks', 'swapPublic', 'GET', {'cost': 10})
    swappublic_get_v3_market_trades = swapPublicGetV3MarketTrades = Entry('v3/market/trades', 'swapPublic', 'GET', {'cost': 0.****************})
    swappublic_get_v3_market_liquidationorder = swapPublicGetV3MarketLiquidationOrder = Entry('v3/market/liquidationOrder', 'swapPublic', 'GET', {'cost': 0.****************})
    swappublic_get_v3_market_tickers = swapPublicGetV3MarketTickers = Entry('v3/market/tickers', 'swapPublic', 'GET', {'cost': 0.****************})
    swappublic_get_v3_market_markprice = swapPublicGetV3MarketMarkPrice = Entry('v3/market/markPrice', 'swapPublic', 'GET', {'cost': 0.****************})
    swappublic_get_v3_market_indexprice = swapPublicGetV3MarketIndexPrice = Entry('v3/market/indexPrice', 'swapPublic', 'GET', {'cost': 0.****************})
    swappublic_get_v3_market_indexpricecomponents = swapPublicGetV3MarketIndexPriceComponents = Entry('v3/market/indexPriceComponents', 'swapPublic', 'GET', {'cost': 0.****************})
    swappublic_get_v3_market_fundingrate = swapPublicGetV3MarketFundingRate = Entry('v3/market/fundingRate', 'swapPublic', 'GET', {'cost': 0.****************})
    swappublic_get_v3_market_openinterest = swapPublicGetV3MarketOpenInterest = Entry('v3/market/openInterest', 'swapPublic', 'GET', {'cost': 0.****************})
    swappublic_get_v3_market_insurance = swapPublicGetV3MarketInsurance = Entry('v3/market/insurance', 'swapPublic', 'GET', {'cost': 0.****************})
    swappublic_get_v3_market_risklimit = swapPublicGetV3MarketRiskLimit = Entry('v3/market/riskLimit', 'swapPublic', 'GET', {'cost': 0.****************})
    swapprivate_get_v3_account_balance = swapPrivateGetV3AccountBalance = Entry('v3/account/balance', 'swapPrivate', 'GET', {'cost': 4})
    swapprivate_get_v3_account_bills = swapPrivateGetV3AccountBills = Entry('v3/account/bills', 'swapPrivate', 'GET', {'cost': 20})
    swapprivate_get_v3_trade_order_opens = swapPrivateGetV3TradeOrderOpens = Entry('v3/trade/order/opens', 'swapPrivate', 'GET', {'cost': 20})
    swapprivate_get_v3_trade_order_trades = swapPrivateGetV3TradeOrderTrades = Entry('v3/trade/order/trades', 'swapPrivate', 'GET', {'cost': 20})
    swapprivate_get_v3_trade_order_history = swapPrivateGetV3TradeOrderHistory = Entry('v3/trade/order/history', 'swapPrivate', 'GET', {'cost': 20})
    swapprivate_get_v3_trade_position_opens = swapPrivateGetV3TradePositionOpens = Entry('v3/trade/position/opens', 'swapPrivate', 'GET', {'cost': 20})
    swapprivate_get_v3_trade_position_history = swapPrivateGetV3TradePositionHistory = Entry('v3/trade/position/history', 'swapPrivate', 'GET', {'cost': 20})
    swapprivate_get_v3_position_leverages = swapPrivateGetV3PositionLeverages = Entry('v3/position/leverages', 'swapPrivate', 'GET', {'cost': 20})
    swapprivate_get_v3_position_mode = swapPrivateGetV3PositionMode = Entry('v3/position/mode', 'swapPrivate', 'GET', {'cost': 20})
    swapprivate_post_v3_trade_order = swapPrivatePostV3TradeOrder = Entry('v3/trade/order', 'swapPrivate', 'POST', {'cost': 4})
    swapprivate_post_v3_trade_orders = swapPrivatePostV3TradeOrders = Entry('v3/trade/orders', 'swapPrivate', 'POST', {'cost': 40})
    swapprivate_post_v3_trade_position = swapPrivatePostV3TradePosition = Entry('v3/trade/position', 'swapPrivate', 'POST', {'cost': 20})
    swapprivate_post_v3_trade_positionall = swapPrivatePostV3TradePositionAll = Entry('v3/trade/positionAll', 'swapPrivate', 'POST', {'cost': 100})
    swapprivate_post_v3_position_leverage = swapPrivatePostV3PositionLeverage = Entry('v3/position/leverage', 'swapPrivate', 'POST', {'cost': 20})
    swapprivate_post_v3_position_mode = swapPrivatePostV3PositionMode = Entry('v3/position/mode', 'swapPrivate', 'POST', {'cost': 20})
    swapprivate_post_v3_trade_position_margin = swapPrivatePostV3TradePositionMargin = Entry('v3/trade/position/margin', 'swapPrivate', 'POST', {'cost': 20})
    swapprivate_delete_v3_trade_order = swapPrivateDeleteV3TradeOrder = Entry('v3/trade/order', 'swapPrivate', 'DELETE', {'cost': 2})
    swapprivate_delete_v3_trade_batchorders = swapPrivateDeleteV3TradeBatchOrders = Entry('v3/trade/batchOrders', 'swapPrivate', 'DELETE', {'cost': 20})
    swapprivate_delete_v3_trade_allorders = swapPrivateDeleteV3TradeAllOrders = Entry('v3/trade/allOrders', 'swapPrivate', 'DELETE', {'cost': 20})
